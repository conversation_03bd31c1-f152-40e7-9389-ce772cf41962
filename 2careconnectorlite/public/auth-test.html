<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Auth Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Direct Supabase Auth Test</h1>
        
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="J4913836j">
        <button onclick="testAuth()">Test Authentication</button>
        
        <div id="result"></div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjEwNzI3MzQsImV4cCI6MjAzNjY0ODczNH0.Ej4rYlJhZGhKdGJhZGhKdGJhZGhKdGJhZGhKdGJhZGhK'
        
        const { createClient } = supabase
        const supabaseClient = createClient(supabaseUrl, supabaseKey)
        
        console.log('Supabase client initialized:', !!supabaseClient)

        async function testAuth() {
            console.log('=== STARTING DIRECT AUTH TEST ===')
            
            const email = document.getElementById('email').value
            const password = document.getElementById('password').value
            const resultDiv = document.getElementById('result')
            
            console.log('Testing with email:', email)
            
            try {
                resultDiv.innerHTML = '<div>Testing authentication...</div>'
                
                const { data, error } = await supabaseClient.auth.signInWithPassword({
                    email: email,
                    password: password,
                })
                
                console.log('Auth response:', { data, error })
                
                if (error) {
                    console.error('Auth error:', error)
                    resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`
                } else {
                    console.log('Auth success:', data.user)
                    resultDiv.innerHTML = `<div class="result success">Success! User: ${data.user.email}<br>Redirecting to dashboard...</div>`
                    
                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = '/dashboard'
                    }, 2000)
                }
            } catch (err) {
                console.error('Catch error:', err)
                resultDiv.innerHTML = `<div class="result error">Catch Error: ${err.message}</div>`
            }
        }
        
        // Test on page load
        console.log('Direct auth test page loaded')
    </script>
</body>
</html>
