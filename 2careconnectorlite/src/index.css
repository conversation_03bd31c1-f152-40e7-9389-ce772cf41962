@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== COLORS ONLY - NO SIZING/SPACING/LAYOUT ===== */
:root {
  /* Brand Colors - ONE SHADE ONLY per Holy Rule 3 - SUBTLE & ELEGANT */
  --primary: #10b981;
  --primary-accent: #10b981;
  --accent: #10b981;
  
  /* Text Colors - Apple Mac Desktop Style */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-muted: #a1a1a6;
  --text-label: #424245;
  --text-white: #ffffff;
  --text-placeholder: #a1a1a6;
  --bg-progress: #e5e7eb;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --status-success-bg: #dcfce7;
  --status-success-text: #166534;
  --status-warning-bg: #fef3c7;
  --status-warning-text: #92400e;
  --status-neutral-bg: #f3f4f6;
  --status-neutral-text: #374151;
  
  /* Background Colors - Apple Mac Desktop Style - NO GREY/BLACK */
  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
  --bg-accent: #ffffff;
  --bg-sidebar: #ffffff;
  --bg-content: #ffffff;
  
  /* Border Colors - Apple Mac Desktop Style */
  --border-light: #d2d2d7;
  --border-medium: #c7c7cc;
  --border-error: #fecaca;
  --border-separator: #e5e5e7;

  /* Focus Colors */
  --focus-shadow: rgba(5, 150, 105, 0.1);

  /* Error Colors */
  --error: #ef4444;
  --text-error: #dc2626;
  --bg-error: #fef2f2;
  --bg-error-light: #fef2f2;

  /* Success Colors */
  --success: #10b981;
  --text-success: #10b981;
  --bg-success: #ecfdf5;

  /* Overlay Colors */
  --overlay-dark: rgba(0, 0, 0, 0.5);

  /* Shadow Colors - Apple Mac Desktop Style */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-large: 0 10px 15px rgba(0, 0, 0, 0.08);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-card-hover: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-icon: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-sidebar: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);

  /* Warning Colors */
  --warning: #f59e0b;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeInScale {
  animation: fadeInScale 0.6s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.7s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.7s ease-out forwards;
}

.animate-pulse-subtle {
  animation: pulse 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* ===== FONT CHARACTER ONLY - NO SIZING ===== */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== ICON STYLES ONLY ===== */
.icon {
  color: var(--primary);
}

.icon-secondary {
  color: var(--text-secondary);
}

/* ===== BUTTON COLOR STYLES ONLY ===== */
.button-primary {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.button-primary:hover {
  background-color: var(--primary);
  opacity: 0.9;
}

.button-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.button-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== DROPDOWN LINK COLOR STYLES ONLY ===== */
.dropdown-link {
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.dropdown-link:hover {
  background-color: var(--bg-secondary);
}

/* ===== DASHBOARD CARD COLOR STYLES ONLY ===== */
.dashboard-card {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.dashboard-card:hover {
  background-color: var(--bg-primary);
}

/* ===== SIDEBAR BUTTON COLOR STYLES ONLY ===== */
.sidebar-button {
  color: var(--text-secondary);
  background-color: transparent;
}

.sidebar-button:hover {
  background-color: var(--bg-secondary);
  color: var(--primary);
}

.sidebar-button.active {
  background-color: var(--bg-accent);
  color: var(--primary);
}

/* ===== MACOS SIDEBAR NAVIGATION COLOR STYLES ONLY ===== */
.macos-sidebar-item {
  color: var(--text-primary);
  background-color: transparent;
}

.macos-sidebar-item:hover {
  background-color: var(--bg-secondary);
}

.macos-sidebar-item.active {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.macos-sidebar-item.active:hover {
  background-color: var(--primary);
  color: var(--bg-primary);
}

/* ===== APPLE MAC DESKTOP TYPOGRAPHY STYLES ===== */
.macos-title {
  font-weight: 600;
  letter-spacing: -0.01em;
  color: var(--text-primary);
}

.macos-subtitle {
  font-weight: 500;
  color: var(--text-secondary);
}

.macos-body {
  font-weight: 400;
  color: var(--text-primary);
}

.macos-caption {
  font-weight: 400;
  color: var(--text-muted);
}

/* ===== APPLE MAC DESKTOP LAYOUT COLOR STYLES ONLY ===== */
.macos-dashboard-layout {
  background-color: var(--bg-secondary);
}

.macos-sidebar {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.macos-main-content {
  background-color: var(--bg-primary);
}

.macos-content-area {
  background-color: var(--bg-secondary);
}

/* ===== APPLE MAC DESKTOP FOCUS STYLES ===== */
.focus-outline {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.focus-shadow {
  box-shadow: 0 0 0 3px var(--focus-shadow);
}

/* ===== PLACEHOLDER STYLES ===== */
.placeholder-styled::placeholder {
  color: var(--text-placeholder);
}

/* ===== FORM VALIDATION STYLES ===== */
.form-input-error {
  border-color: var(--error) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.form-input-success {
  border-color: var(--success) !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* ===== NAVIGATION STYLES ===== */
.nav-current-page {
  position: relative;
}

.nav-current-page::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: var(--primary);
  border-radius: 2px;
}

/* ===== STANDARDIZED BUTTON STYLES ===== */
.btn-primary {
  background-color: var(--primary);
  color: var(--bg-primary);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary);
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--bg-accent);
  border-color: var(--primary);
}

.btn-icon {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--bg-accent);
  color: var(--primary);
}

/* ===== ICON SIZING MOVED TO COMPONENT FILES ===== */
/* Icon sizes must be defined in individual component files, not index.css */

/* ===== SPACING MOVED TO COMPONENT FILES ===== */
/* Spacing must be defined in individual component files, not index.css */

/* ===== ACCESSIBILITY FEATURES ===== */
/* Focus Indicators */
.focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
a:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.1);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --bg-primary: #ffffff;
    --bg-secondary: #f0f0f0;
    --border-light: #666666;
    --border-medium: #333333;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--bg-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* ===== STATUS CLASSES ===== */
.bg-success {
  background-color: var(--bg-success);
}

.text-success-text {
  color: var(--text-success);
}

.bg-warning {
  background-color: var(--warning);
  opacity: 0.1;
}

.text-warning-text {
  color: var(--warning);
}

.bg-neutral {
  background-color: var(--text-secondary);
  opacity: 0.1;
}

.text-neutral-text {
  color: var(--text-secondary);
}

/* ===== RESPONSIVE LAYOUT MOVED TO COMPONENT FILES ===== */
/* All responsive sizing, spacing, and layout must be in component files, not index.css */
