import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Calendar as CalendarIcon, Clock, ArrowLeft, CheckCircle2, <PERSON><PERSON><PERSON>riangle, RefreshCw, Lock } from 'lucide-react';
import { format, parseISO, isAfter, isBefore } from 'date-fns';

interface Booking {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  provider_name?: string;
  created_at: string;
}

const RescheduleBookingPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookingId } = useParams<{ bookingId: string }>();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [rescheduling, setRescheduling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [newStartTime, setNewStartTime] = useState('');
  const [newEndTime, setNewEndTime] = useState('');
  const [rescheduleReason, setRescheduleReason] = useState('');

  useEffect(() => {
    const fetchBooking = async () => {
      if (!bookingId) {
        setError("Booking ID is missing.");
        setLoading(false);
        return;
      }

      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError("Please sign in to reschedule your booking.");
          setLoading(false);
          return;
        }

        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id) // Ensure user owns this booking
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          throw new Error("Booking not found or you don't have permission to reschedule it.");
        }

        // Check if booking can be rescheduled
        if (!['confirmed', 'pending'].includes(data.status)) {
          throw new Error("This booking cannot be rescheduled due to its current status.");
        }

        console.log('Booking data fetched successfully:', data);
        setBooking(data);
        
        // Set initial values to current booking times
        setNewStartTime(data.start_time.replace('Z', '').slice(0, 16)); // Convert to local datetime format
        setNewEndTime(data.end_time.replace('Z', '').slice(0, 16));

      } catch (err: any) {
        console.error("Error fetching booking details:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBooking();
  }, [bookingId]);

  const calculateDuration = () => {
    if (newStartTime && newEndTime) {
      const start = new Date(newStartTime);
      const end = new Date(newEndTime);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);
      return Math.max(0, diffHours);
    }
    return 0;
  };

  const calculateNewTotalCost = () => {
    if (!booking) return 0;
    
    const oldDuration = (new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60);
    const newDuration = calculateDuration();
    const ratePerHour = oldDuration > 0 ? booking.total_cost / oldDuration : 0;
    
    return newDuration * ratePerHour;
  };

  const validateNewTimes = () => {
    if (!newStartTime || !newEndTime) {
      return "Please select both start and end times.";
    }

    if (new Date(newStartTime) >= new Date(newEndTime)) {
      return "End time must be after start time.";
    }

    if (calculateDuration() < 0.5) {
      return "Booking must be at least 30 minutes.";
    }

    if (isBefore(new Date(newStartTime), new Date())) {
      return "Start time cannot be in the past.";
    }

    // Check if times have actually changed
    if (booking && 
        newStartTime === booking.start_time.replace('Z', '').slice(0, 16) && 
        newEndTime === booking.end_time.replace('Z', '').slice(0, 16)) {
      return "Please select different times to reschedule.";
    }

    return null;
  };

  const handleReschedule = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateNewTimes();
    if (validationError) {
      alert(validationError);
      return;
    }

    if (!rescheduleReason.trim()) {
      alert('Please provide a reason for rescheduling.');
      return;
    }

    setRescheduling(true);
    try {
      const updatedBookingData = {
        start_time: new Date(newStartTime).toISOString(),
        end_time: new Date(newEndTime).toISOString(),
        total_cost: calculateNewTotalCost(),
        status: 'pending', // Reset to pending for provider confirmation
        reschedule_reason: rescheduleReason,
        rescheduled_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update(updatedBookingData)
        .eq('id', booking!.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log('Booking rescheduled successfully:', data);
      
      // Navigate back to booking detail page with success message
      navigate(`/booking/${booking!.id}`, { 
        state: { message: 'Booking rescheduled successfully! Waiting for provider confirmation.' }
      });

    } catch (err: any) {
      console.error('Error rescheduling booking:', err);
      alert(`Failed to reschedule booking: ${err.message}`);
    } finally {
      setRescheduling(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading booking details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    // Check if this is an authentication error
    const isAuthError = error.includes('sign in') || error.includes('Please sign in');
    
    if (isAuthError) {
      return (
        <div className="auth-error-container">
          <div className="auth-error-content">
            <div className="text-center">
              <Lock className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h2>
              <p className="text-gray-600 mb-6">Please sign in to reschedule bookings and access this feature.</p>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/auth')}
                  className="auth-error-primary-button w-full"
                >
                  Sign In
                </button>
                <button
                  onClick={() => navigate(-1)}
                  className="auth-error-secondary-button w-full"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // General error state for non-authentication errors
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Cannot Reschedule</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => navigate(-1)}
              className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Not Found</h2>
            <p className="text-gray-600 mb-6">The booking you are trying to reschedule could not be found.</p>
            <button
              onClick={() => navigate('/my-bookings')}
              className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              View My Bookings
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate(`/booking/${booking.id}`)}
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4 font-medium"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Booking
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Reschedule Booking</h1>
              <p className="text-gray-600">Booking ID: {booking.id}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleReschedule} className="space-y-6">
              
              {/* Current Booking Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Current Booking Details</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Service</label>
                    <p className="text-gray-900 font-medium">{booking.service_type}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Provider</label>
                    <p className="text-gray-900 font-medium">{booking.provider_name || 'Care Provider'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Current Date</label>
                    <p className="text-gray-900 font-medium">{format(parseISO(booking.start_time), 'MMMM d, yyyy')}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Current Time</label>
                    <p className="text-gray-900 font-medium">
                      {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Current Cost</label>
                    <p className="text-gray-900 font-medium">${booking.total_cost}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                    <p className="text-gray-900 font-medium capitalize">{booking.status}</p>
                  </div>
                </div>

                {booking.location && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-500 mb-1">Location</label>
                    <p className="text-gray-900">{booking.location}</p>
                  </div>
                )}

                {booking.special_requirements && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Special Requirements</label>
                    <p className="text-gray-900">{booking.special_requirements}</p>
                  </div>
                )}
              </div>

              {/* New Date and Time */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Select New Date & Time</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">New Start Time</label>
                    <input
                      type="datetime-local"
                      value={newStartTime}
                      onChange={(e) => setNewStartTime(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">New End Time</label>
                    <input
                      type="datetime-local"
                      value={newEndTime}
                      onChange={(e) => setNewEndTime(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                      required
                    />
                  </div>
                </div>
                
                {newStartTime && newEndTime && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-xl">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">New Duration:</span>
                        <span className="font-medium text-gray-900">{calculateDuration().toFixed(1)} hours</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">New Cost:</span>
                        <span className="font-medium text-var(--logo-green)">${calculateNewTotalCost().toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Reschedule Reason */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Reason for Rescheduling</h2>
                
                <textarea
                  placeholder="Please explain why you need to reschedule this booking..."
                  value={rescheduleReason}
                  onChange={(e) => setRescheduleReason(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent resize-none"
                  required
                />
                
                <p className="mt-2 text-sm text-gray-600">
                  This information will be shared with your care provider to help them understand the change.
                </p>
              </div>
            </form>
          </div>

          {/* Reschedule Summary Sidebar */}
          <div className="space-y-6">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Reschedule Summary</h3>
              
              <div className="space-y-4 mb-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Current Booking</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>{format(parseISO(booking.start_time), 'EEEE, MMM d, yyyy')}</p>
                    <p>{format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}</p>
                    <p className="font-medium">${booking.total_cost}</p>
                  </div>
                </div>
                
                {newStartTime && newEndTime && (
                  <>
                    <div className="border-t border-gray-200 pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">New Booking</h4>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>{format(new Date(newStartTime), 'EEEE, MMM d, yyyy')}</p>
                        <p>{format(new Date(newStartTime), 'h:mm a')} - {format(new Date(newEndTime), 'h:mm a')}</p>
                        <p className="font-medium text-var(--logo-green)">${calculateNewTotalCost().toFixed(2)}</p>
                      </div>
                    </div>
                    
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Cost Difference:</span>
                        <span className={`font-medium ${
                          calculateNewTotalCost() > booking.total_cost 
                            ? 'text-red-600' 
                            : calculateNewTotalCost() < booking.total_cost 
                              ? 'text-var(--logo-green)' 
                              : 'text-gray-900'
                        }`}>
                          {calculateNewTotalCost() > booking.total_cost ? '+' : ''}
                          ${(calculateNewTotalCost() - booking.total_cost).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              <div className="space-y-3">
                <button
                  type="submit"
                  onClick={handleReschedule}
                  disabled={rescheduling || !newStartTime || !newEndTime || !rescheduleReason.trim()}
                  className="w-full flex items-center justify-center px-6 py-4 bg-var(--logo-green) text-white rounded-xl font-medium hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {rescheduling ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Rescheduling...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Confirm Reschedule
                    </>
                  )}
                </button>
                
                <button
                  type="button"
                  onClick={() => navigate(`/booking/${booking.id}`)}
                  className="w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
                >
                  Cancel Changes
                </button>
              </div>
              
              <div className="mt-4 p-4 bg-yellow-50 rounded-xl">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div className="text-xs text-yellow-800">
                    <p className="font-medium mb-1">Important Notes:</p>
                    <ul className="space-y-1">
                      <li>• Your booking will be marked as "pending" until the provider confirms</li>
                      <li>• The provider may decline the reschedule request</li>
                      <li>• You'll be notified of the provider's decision</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RescheduleBookingPage;
