import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, X, Calendar as CalendarIcon, User, Clock, MapPin, DollarSign, AlertTriangle, ArrowLeft, CheckCircle, Lock } from 'lucide-react';
import { format, parseISO, isBefore, differenceInHours } from 'date-fns';

interface BookingCancellation {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  created_at: string;
  provider_name?: string;
  provider_email?: string;
  provider_phone?: string;
}

const BookingCancellationPage: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();
  
  const [booking, setBooking] = useState<BookingCancellation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancelling, setCancelling] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const [showCancellationForm, setShowCancellationForm] = useState(false);
  const [cancelled, setCancelled] = useState(false);

  useEffect(() => {
    const fetchBookingForCancellation = async () => {
      if (!bookingId) {
        setError("Invalid booking ID.");
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to cancel your booking.");
          setLoading(false);
          return;
        }

        // Fetch booking details
        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id)
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          setError("Booking not found or you don't have permission to cancel it.");
          setLoading(false);
          return;
        }

        // Check if booking is already cancelled
        if (['cancelled_by_user', 'cancelled_by_provider'].includes(data.status)) {
          setError("This booking has already been cancelled.");
          setLoading(false);
          return;
        }

        // Check if booking is in the past
        if (isBefore(parseISO(data.start_time), new Date())) {
          setError("Cannot cancel a booking that has already started or passed.");
          setLoading(false);
          return;
        }

        // Fetch provider details
        const { data: providerData, error: providerError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('full_name, email, phone')
          .eq('id', data.provider_id)
          .single();

        if (providerError) {
          console.warn('Could not fetch provider details:', providerError);
        }

        const bookingWithProvider = {
          ...data,
          provider_name: providerData?.full_name || 'Provider',
          provider_email: providerData?.email || '',
          provider_phone: providerData?.phone || ''
        };

        console.log('Booking for cancellation fetched successfully:', bookingWithProvider);
        setBooking(bookingWithProvider);
      } catch (err: any) {
        console.error("Error fetching booking for cancellation:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingForCancellation();
  }, [bookingId]);

  const getCancellationPolicy = () => {
    if (!booking) return null;
    
    const hoursUntilStart = differenceInHours(parseISO(booking.start_time), new Date());
    
    if (hoursUntilStart >= 24) {
      return {
        type: 'full',
        refund: booking.total_cost,
        message: 'Full refund available - cancelling more than 24 hours in advance'
      };
    } else if (hoursUntilStart >= 2) {
      return {
        type: 'partial',
        refund: booking.total_cost * 0.5,
        message: 'Partial refund (50%) - cancelling 2-24 hours in advance'
      };
    } else {
      return {
        type: 'none',
        refund: 0,
        message: 'No refund - cancelling less than 2 hours in advance'
      };
    }
  };

  const handleCancelBooking = async () => {
    if (!booking || !cancellationReason.trim()) {
      alert('Please provide a reason for cancellation.');
      return;
    }

    setCancelling(true);
    try {
      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update({ 
          status: 'cancelled_by_user',
          cancellation_reason: cancellationReason.trim()
        })
        .eq('id', booking.id);

      if (error) {
        throw error;
      }

      setCancelled(true);
      alert('Booking cancelled successfully!');
    } catch (err: any) {
      console.error('Error cancelling booking:', err);
      alert('Failed to cancel booking. Please try again or contact support.');
    } finally {
      setCancelling(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading booking details...</span>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header with Navigation */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Booking Cancellation</h1>
                <p className="text-gray-600 mt-1">Cancel your booking request</p>
              </div>
              <Link
                to="/dashboard"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Authentication Error State */}
        <div className="auth-error-container">
          <div className="auth-error-content">
            <Lock className="auth-error-icon" />
            <h2 className="auth-error-title">Access Restricted</h2>
            <p className="auth-error-message">{error || "Please sign in to cancel your booking."}</p>
            <div className="auth-error-buttons">
              <button
                onClick={() => navigate('/auth')}
                className="auth-error-primary-button"
              >
                Sign In
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="auth-error-secondary-button"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (cancelled) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-var(--logo-green) mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Cancelled</h2>
            <p className="text-gray-600 mb-6">Your booking has been successfully cancelled. You will receive a confirmation email shortly.</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate('/my-bookings')}
                className="flex-1 bg-gray-100 text-gray-700 px-4 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                My Bookings
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="flex-1 bg-var(--logo-green) text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const cancellationPolicy = getCancellationPolicy();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Cancel Booking</h1>
                <p className="text-gray-600">Review and cancel your appointment</p>
              </div>
            </div>
            <X className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Booking Details Card */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <p className="text-sm text-gray-500 mb-1">Service Type</p>
              <p className="font-medium text-gray-900">{booking.service_type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Provider</p>
              <p className="font-medium text-gray-900">{booking.provider_name}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p className="font-medium text-gray-900">{format(parseISO(booking.start_time), 'MMMM d, yyyy')}</p>
                <p className="text-sm text-gray-500">{format(parseISO(booking.start_time), 'EEEE')}</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Time</p>
                <p className="font-medium text-gray-900">
                  {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                </p>
                <p className="text-sm text-gray-500">
                  {((new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)} hours
                </p>
              </div>
            </div>
            
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Total Cost</p>
                <p className="font-medium text-gray-900">${booking.total_cost}</p>
              </div>
            </div>
          </div>

          {booking.location && (
            <div className="mt-6 flex items-center">
              <MapPin className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Location</p>
                <p className="font-medium text-gray-900">{booking.location}</p>
              </div>
            </div>
          )}
        </div>

        {/* Cancellation Policy */}
        {cancellationPolicy && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-start">
              <AlertTriangle className={`h-6 w-6 mr-3 mt-1 ${cancellationPolicy.type === 'full' ? 'text-var(--logo-green)' : cancellationPolicy.type === 'partial' ? 'text-yellow-500' : 'text-red-500'}`} />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Cancellation Policy</h3>
                <p className="text-gray-700 mb-3">{cancellationPolicy.message}</p>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Original Cost:</span>
                    <span className="font-medium text-gray-900">${booking.total_cost}</span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-gray-600">Refund Amount:</span>
                    <span className={`font-medium ${cancellationPolicy.refund > 0 ? 'text-var(--logo-green)' : 'text-red-600'}`}>
                      ${cancellationPolicy.refund}
                    </span>
                  </div>
                  {cancellationPolicy.refund < booking.total_cost && (
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-gray-600">Cancellation Fee:</span>
                      <span className="font-medium text-red-600">
                        ${booking.total_cost - cancellationPolicy.refund}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Cancellation Form or Button */}
        {!showCancellationForm ? (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel This Booking</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel this booking? This action cannot be undone.
            </p>
            <div className="flex gap-4">
              <button
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Keep Booking
              </button>
              <button
                onClick={() => setShowCancellationForm(true)}
                className="flex-1 bg-red-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-red-700 transition-colors"
              >
                Continue Cancellation
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Reason for Cancellation</h3>
            <p className="text-gray-600 mb-4">
              Please provide a reason for cancelling this booking. This helps us improve our service.
            </p>
            
            <textarea
              value={cancellationReason}
              onChange={(e) => setCancellationReason(e.target.value)}
              placeholder="Please explain why you need to cancel this booking..."
              className="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent mb-6"
              rows={4}
              required
            />

            <div className="flex gap-4">
              <button
                onClick={() => setShowCancellationForm(false)}
                className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                disabled={cancelling}
              >
                Go Back
              </button>
              <button
                onClick={handleCancelBooking}
                disabled={cancelling || !cancellationReason.trim()}
                className="flex-1 bg-red-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {cancelling ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Cancelling...
                  </div>
                ) : (
                  'Confirm Cancellation'
                )}
              </button>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
          <p className="text-gray-600 mb-4">
            If you have questions about cancelling your booking or need assistance, our support team is here to help.
          </p>
          <div className="flex gap-4">
            <Link
              to="/support"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Contact Support
            </Link>
            <Link
              to="/help/cancellation-policy"
              className="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors border border-blue-200"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCancellationPage;
