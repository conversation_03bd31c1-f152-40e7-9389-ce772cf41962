import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, Filter, MapPin, Clock, DollarSign, Star } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function CareCheckers() {
  const navigate = useNavigate()
  const [careCheckers, setCareCheckers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [location, setLocation] = useState('')
  const [availability, setAvailability] = useState('Any availability')
  const [maxRate, setMaxRate] = useState(200)
  const [minExperience, setMinExperience] = useState(0)



  useEffect(() => {
    async function fetchCareCheckers() {
      try {
        setLoading(true)
        console.log('🔍 Fetching care checkers data...')
        // Fetch real care checkers data from database via dataService
        const data = await dataService.getCareCheckers()
        console.log('✅ Care checkers data received:', data)
        console.log('📊 Number of care checkers:', data?.length || 0)
        setCareCheckers(data || [])
      } catch (err) {
        console.error('❌ Error loading care checkers:', err)
        setCareCheckers([]) // Set empty array on error
      } finally {
        setLoading(false)
      }
    }
    fetchCareCheckers()
  }, [])

  const handleSearch = () => {
    // Implement search functionality
    console.log('Searching with filters:', { searchTerm, location, availability, maxRate, minExperience })
  }

  const resetFilters = () => {
    setSearchTerm('')
    setLocation('')
    setAvailability('Any availability')
    setMaxRate(200)
    setMinExperience(0)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Care Checkers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding quality assurance professionals for you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Filter care checkers based on search criteria
  const filteredCareCheckers = careCheckers.filter(checker => {
    const matchesName = !searchTerm || checker.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesLocation = !location || checker.location.toLowerCase().includes(location.toLowerCase())
    const matchesRate = !checker.hourly_rate || checker.hourly_rate <= maxRate
    const matchesExperience = !checker.experience_years || checker.experience_years >= minExperience
    
    return matchesName && matchesLocation && matchesRate && matchesExperience
  })

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header - Elegant & Clean */}
      <div className="page-header px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>Find Care Checkers</h1>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
            {filteredCareCheckers.length} quality assurance professional{filteredCareCheckers.length !== 1 ? 's' : ''} available
          </p>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar Filters */}
        <div className="w-80 bg-white border-r border-gray-200 p-6">
          <div className="flex items-center gap-2 mb-6">
            <Filter className="w-5 h-5 text-emerald-600" />
            <h2 className="text-lg font-semibold text-gray-900">Search & Filters</h2>
          </div>

          <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }}>
            {/* Search by name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Search by name</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search for care checkers by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
            </div>
          </div>

          {/* Location */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Enter location..."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
            </div>
          </div>

          {/* Availability */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Availability</label>
            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option>Any availability</option>
              <option>Available now</option>
              <option>Available within 24 hours</option>
              <option>Available within week</option>
            </select>
          </div>

          {/* Max Hourly Rate */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Hourly Rate: ${maxRate}</label>
            <input
              type="range"
              min="20"
              max="200"
              value={maxRate}
              onChange={(e) => setMaxRate(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${((maxRate - 20) / 180) * 100}%, var(--border-light) ${((maxRate - 20) / 180) * 100}%, var(--border-light) 100%)`
              }}
            />
          </div>

          {/* Min Years Experience */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Min Years Experience: {minExperience}</label>
            <input
              type="range"
              min="0"
              max="20"
              value={minExperience}
              onChange={(e) => setMinExperience(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(minExperience / 20) * 100}%, var(--border-light) ${(minExperience / 20) * 100}%, var(--border-light) 100%)`
              }}
            />
          </div>

            {/* Search Button */}
            <button
              type="submit"
              className="w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors duration-200 flex items-center justify-center gap-2 mb-4"
            >
              <Search className="w-4 h-4" />
              Search
            </button>
          </form>

          {/* Reset Filters */}
          <button
            onClick={resetFilters}
            className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Reset Filters
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {filteredCareCheckers.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No care checkers found matching your criteria.</p>
            </div>
          ) : (
            <div className="space-y-8">
              {filteredCareCheckers.map((checker) => (
                <div key={checker.id} className="group rounded-2xl p-8 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-2xl animate-fadeInUp" style={{
                  backgroundColor: 'var(--bg-primary)',
                  border: '2px solid var(--border-light)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
                }}>
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg ring-4 ring-accent group-hover:ring-primary transition-all duration-300" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <span className="font-bold text-lg" style={{ color: 'var(--primary)' }}>
                          {checker.initials || checker.name?.split(' ').map((n: string) => n[0]).join('') || 'C'}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold group-hover:text-primary transition-colors" style={{ color: 'var(--text-primary)' }}>{checker.full_name || `${checker.first_name || ''} ${checker.last_name || ''}`.trim()}</h3>
                        <div className="flex items-center gap-2">
                          {checker.is_verified && (
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">Verified Provider</span>
                          )}
                          <span className="text-xs flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                            <span className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></span>
                            Verified
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="flex items-center gap-2 mb-2" style={{ color: 'var(--text-secondary)' }}>
                      <MapPin className="w-4 h-4" />
                      {checker.location}
                    </p>
                    {checker.needs && (
                      <p className="text-sm" style={{ color: 'var(--text-primary)' }}>
                        {checker.needs}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <span className="font-semibold flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                        <DollarSign className="w-4 h-4" />
                        ${checker.hourly_rate}/hour
                      </span>
                      {checker.years_of_experience && (
                        <span className="text-sm flex items-center gap-1" style={{ color: 'var(--text-secondary)' }}>
                          <Clock className="w-4 h-4" />
                          {checker.years_of_experience} years
                        </span>
                      )}
                    </div>
                  </div>

                  <button 
                    onClick={() => navigate(`/provider/care-checkers/${checker.id}`)}
                    className="button-primary"
                  >
                    View Profile
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer
        className="footer"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: 'var(--primary)' }}
              >
                <span className="font-bold text-sm" style={{ color: 'var(--bg-primary)' }}>CC</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Care Connector</h3>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Modern healthcare coordination.</p>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Services</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>Find Care</li>
                  <li>Care Groups</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Learn More</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>How it Works</li>
                  <li>Features</li>
                </ul>
              </div>
            </div>
          </div>
          <div
            className="flex items-center justify-center gap-6 mt-6 pt-6"
            style={{ borderTop: '1px solid var(--border-light)' }}
          >
            <div className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: 'var(--primary)' }}
              ></span>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>HIPAA Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4" style={{ color: 'var(--primary)' }} />
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Verified Providers</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
