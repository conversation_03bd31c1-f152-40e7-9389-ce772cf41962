import React, { useState, useEffect } from 'react'
import { Setting<PERSON>, <PERSON>, Calendar, MapPin, DollarSign, Clock, Save, Check, User, CreditCard } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface BookingPreferences {
  notifications: {
    email_confirmations: boolean
    sms_reminders: boolean
    push_notifications: boolean
    reminder_hours: number
    cancellation_alerts: boolean
  }
  availability: {
    preferred_days: string[]
    preferred_times: string[]
    advance_booking_days: number
    last_minute_booking: boolean
  }
  location: {
    preferred_areas: string[]
    max_travel_distance: number
    in_home_care: boolean
    facility_care: boolean
  }
  providers: {
    preferred_types: string[]
    gender_preference: string
    languages: string[]
    min_experience_years: number
    min_rating: number
  }
  payment: {
    default_method: string
    auto_payment: boolean
    payment_reminders: boolean
    invoice_method: string
  }
}

const BookingPreferencesPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [activeTab, setActiveTab] = useState('notifications')

  const [preferences, setPreferences] = useState<BookingPreferences>({
    notifications: {
      email_confirmations: true,
      sms_reminders: true,
      push_notifications: true,
      reminder_hours: 24,
      cancellation_alerts: true
    },
    availability: {
      preferred_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      preferred_times: ['morning', 'afternoon'],
      advance_booking_days: 30,
      last_minute_booking: false
    },
    location: {
      preferred_areas: [],
      max_travel_distance: 25,
      in_home_care: true,
      facility_care: true
    },
    providers: {
      preferred_types: ['caregiver', 'companion'],
      gender_preference: 'no_preference',
      languages: ['english'],
      min_experience_years: 2,
      min_rating: 4.0
    },
    payment: {
      default_method: 'credit_card',
      auto_payment: false,
      payment_reminders: true,
      invoice_method: 'email'
    }
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadPreferences(user.id)
      }
      setLoading(false)
    }
    getUser()
  }, [])

  const loadPreferences = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('booking_preferences')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading preferences:', error)
        return
      }

      if (data?.booking_preferences) {
        setPreferences(prev => ({
          ...prev,
          ...data.booking_preferences
        }))
      }
    } catch (error) {
      console.error('Error loading preferences:', error)
    }
  }

  const savePreferences = async () => {
    if (!user) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          booking_preferences: preferences,
          updated_at: new Date().toISOString()
        })

      if (error) throw error

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving preferences:', error)
    } finally {
      setSaving(false)
    }
  }

  const updatePreference = (category: keyof BookingPreferences, key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const toggleArrayValue = (category: keyof BookingPreferences, key: string, value: string) => {
    setPreferences(prev => {
      const currentArray = (prev[category] as any)[key] as string[]
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value]
      
      return {
        ...prev,
        [category]: {
          ...prev[category],
          [key]: newArray
        }
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading preferences...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Settings className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Preferences Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to manage your booking preferences.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'availability', label: 'Availability', icon: Calendar },
    { id: 'location', label: 'Location', icon: MapPin },
    { id: 'providers', label: 'Providers', icon: User },
    { id: 'payment', label: 'Payment', icon: CreditCard }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Preferences</h1>
          <p className="text-gray-600">Customize your booking experience and set your preferences</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-logo-green text-logo-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">Email Confirmations</label>
                      <p className="text-sm text-gray-600">Receive booking confirmations via email</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.notifications.email_confirmations}
                      onChange={(e) => updatePreference('notifications', 'email_confirmations', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">SMS Reminders</label>
                      <p className="text-sm text-gray-600">Get text message reminders before appointments</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.notifications.sms_reminders}
                      onChange={(e) => updatePreference('notifications', 'sms_reminders', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">Push Notifications</label>
                      <p className="text-sm text-gray-600">Receive push notifications for important updates</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.notifications.push_notifications}
                      onChange={(e) => updatePreference('notifications', 'push_notifications', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-2">
                      Reminder Timing
                    </label>
                    <select
                      value={preferences.notifications.reminder_hours}
                      onChange={(e) => updatePreference('notifications', 'reminder_hours', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    >
                      <option value={1}>1 hour before</option>
                      <option value={2}>2 hours before</option>
                      <option value={4}>4 hours before</option>
                      <option value={12}>12 hours before</option>
                      <option value={24}>24 hours before</option>
                      <option value={48}>48 hours before</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Availability Tab */}
            {activeTab === 'availability' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Availability Preferences</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-3">Preferred Days</label>
                  <div className="grid grid-cols-3 md:grid-cols-7 gap-2">
                    {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                      <button
                        key={day}
                        onClick={() => toggleArrayValue('availability', 'preferred_days', day)}
                        className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                          preferences.availability.preferred_days.includes(day)
                            ? 'bg-logo-green text-white border-logo-green'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {day.charAt(0).toUpperCase() + day.slice(1, 3)}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-3">Preferred Time Slots</label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {['early_morning', 'morning', 'afternoon', 'evening', 'night'].map((time) => (
                      <button
                        key={time}
                        onClick={() => toggleArrayValue('availability', 'preferred_times', time)}
                        className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                          preferences.availability.preferred_times.includes(time)
                            ? 'bg-logo-green text-white border-logo-green'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {time.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Advance Booking Limit (days)
                  </label>
                  <select
                    value={preferences.availability.advance_booking_days}
                    onChange={(e) => updatePreference('availability', 'advance_booking_days', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value={7}>1 week</option>
                    <option value={14}>2 weeks</option>
                    <option value={30}>1 month</option>
                    <option value={60}>2 months</option>
                    <option value={90}>3 months</option>
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Allow Last-Minute Bookings</label>
                    <p className="text-sm text-gray-600">Accept bookings within 24 hours</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.availability.last_minute_booking}
                    onChange={(e) => updatePreference('availability', 'last_minute_booking', e.target.checked)}
                    className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                  />
                </div>
              </div>
            )}

            {/* Location Tab */}
            {activeTab === 'location' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Location Preferences</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Maximum Travel Distance (miles)
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="100"
                    value={preferences.location.max_travel_distance}
                    onChange={(e) => updatePreference('location', 'max_travel_distance', Number(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-600 mt-1">
                    <span>5 miles</span>
                    <span>{preferences.location.max_travel_distance} miles</span>
                    <span>100 miles</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">In-Home Care</label>
                      <p className="text-sm text-gray-600">Receive care services at your home</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.location.in_home_care}
                      onChange={(e) => updatePreference('location', 'in_home_care', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">Facility Care</label>
                      <p className="text-sm text-gray-600">Receive care at healthcare facilities</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.location.facility_care}
                      onChange={(e) => updatePreference('location', 'facility_care', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Providers Tab */}
            {activeTab === 'providers' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Provider Preferences</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-3">Preferred Provider Types</label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    {['caregiver', 'companion', 'care_checker'].map((type) => (
                      <button
                        key={type}
                        onClick={() => toggleArrayValue('providers', 'preferred_types', type)}
                        className={`px-4 py-2 text-sm rounded-lg border transition-colors ${
                          preferences.providers.preferred_types.includes(type)
                            ? 'bg-logo-green text-white border-logo-green'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">Gender Preference</label>
                  <select
                    value={preferences.providers.gender_preference}
                    onChange={(e) => updatePreference('providers', 'gender_preference', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value="no_preference">No Preference</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Minimum Experience (years)
                  </label>
                  <select
                    value={preferences.providers.min_experience_years}
                    onChange={(e) => updatePreference('providers', 'min_experience_years', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value={0}>No minimum</option>
                    <option value={1}>1+ years</option>
                    <option value={2}>2+ years</option>
                    <option value={3}>3+ years</option>
                    <option value={5}>5+ years</option>
                    <option value={10}>10+ years</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Minimum Rating
                  </label>
                  <select
                    value={preferences.providers.min_rating}
                    onChange={(e) => updatePreference('providers', 'min_rating', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value={0}>No minimum</option>
                    <option value={3}>3+ stars</option>
                    <option value={3.5}>3.5+ stars</option>
                    <option value={4}>4+ stars</option>
                    <option value={4.5}>4.5+ stars</option>
                  </select>
                </div>
              </div>
            )}

            {/* Payment Tab */}
            {activeTab === 'payment' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Payment Preferences</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">Default Payment Method</label>
                  <select
                    value={preferences.payment.default_method}
                    onChange={(e) => updatePreference('payment', 'default_method', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value="credit_card">Credit Card</option>
                    <option value="debit_card">Debit Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="digital_wallet">Digital Wallet</option>
                  </select>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">Enable Auto-Payment</label>
                      <p className="text-sm text-gray-600">Automatically pay for confirmed bookings</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.payment.auto_payment}
                      onChange={(e) => updatePreference('payment', 'auto_payment', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-900">Payment Reminders</label>
                      <p className="text-sm text-gray-600">Receive reminders for pending payments</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={preferences.payment.payment_reminders}
                      onChange={(e) => updatePreference('payment', 'payment_reminders', e.target.checked)}
                      className="w-4 h-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">Invoice Delivery</label>
                  <select
                    value={preferences.payment.invoice_method}
                    onChange={(e) => updatePreference('payment', 'invoice_method', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="postal_mail">Postal Mail</option>
                    <option value="digital_only">Digital Only</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                Changes are saved automatically when you update preferences
              </div>
              <div className="flex items-center gap-4">
                {saved && (
                  <div className="flex items-center gap-2 text-green-600">
                    <Check className="w-4 h-4" />
                    <span className="text-sm">Saved successfully</span>
                  </div>
                )}
                <button
                  onClick={savePreferences}
                  disabled={saving}
                  className="bg-logo-green text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => window.location.href = '/booking-search'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
                <div className="font-medium text-gray-900">Search Providers</div>
                <div className="text-sm text-gray-600">Find providers matching your preferences</div>
              </button>
              <button
                onClick={() => window.location.href = '/my-bookings'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <Calendar className="w-6 h-6 text-logo-green mb-2" />
                <div className="font-medium text-gray-900">My Bookings</div>
                <div className="text-sm text-gray-600">View your appointments</div>
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
                <div className="font-medium text-gray-900">Dashboard</div>
                <div className="text-sm text-gray-600">Return to dashboard</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingPreferencesPage
