import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { 
  User, 
  Bell, 
  Shield, 
  Globe, 
  Smartphone, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff,
  Save,
  AlertCircle
} from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  location: string | null
  bio: string | null
  role: string
  profile_image_url: string | null
  notification_preferences: any
  privacy_settings: any
}

const Settings: React.FC = () => {
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeSection, setActiveSection] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    appointmentReminders: true,
    messageNotifications: true,
    careGroupUpdates: true,
    marketingEmails: false
  })
  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    showEmail: false,
    showPhone: false,
    allowMessages: true,
    allowConnectionRequests: true
  })

  useEffect(() => {
    getCurrentUser()
  }, [])

  const getCurrentUser = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authUser.id)
          .single()
        
        if (profile) {
          setUser(profile)
          // Load notification preferences
          if (profile.notification_preferences) {
            setNotifications({ ...notifications, ...profile.notification_preferences })
          }
          // Load privacy settings
          if (profile.privacy_settings) {
            setPrivacy({ ...privacy, ...profile.privacy_settings })
          }
        }
      }
    } catch (error) {
      console.error('Error getting current user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProfileUpdate = async (field: string, value: string) => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ [field]: value })
        .eq('id', user.id)

      if (error) throw error
      
      setUser({ ...user, [field]: value })
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleNotificationUpdate = async () => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ notification_preferences: notifications })
        .eq('id', user.id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating notifications:', error)
    } finally {
      setSaving(false)
    }
  }

  const handlePrivacyUpdate = async () => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ privacy_settings: privacy })
        .eq('id', user.id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating privacy settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match')
      return
    }

    try {
      setSaving(true)
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      })

      if (error) throw error
      
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      alert('Password updated successfully')
    } catch (error) {
      console.error('Error updating password:', error)
      alert('Error updating password')
    } finally {
      setSaving(false)
    }
  }

  const sections = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'security', label: 'Security', icon: Lock },
    { id: 'preferences', label: 'Preferences', icon: Globe }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header */}
      <div className="px-6 py-8" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Settings
          </h1>
          <p className="text-lg mt-2" style={{ color: 'var(--text-secondary)' }}>
            Manage your account preferences and privacy settings
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="flex gap-8">
          {/* Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
              <nav className="space-y-2">
                {sections.map((section) => {
                  const Icon = section.icon
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all duration-200 ${
                        activeSection === section.id ? 'shadow-sm' : ''
                      }`}
                      style={{
                        backgroundColor: activeSection === section.id ? 'var(--bg-accent)' : 'transparent',
                        color: activeSection === section.id ? 'var(--primary)' : 'var(--text-secondary)'
                      }}
                    >
                      <Icon className="w-5 h-5" />
                      {section.label}
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="p-6 rounded-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
              {activeSection === 'profile' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Profile Information
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={user?.full_name || ''}
                        onChange={(e) => handleProfileUpdate('full_name', e.target.value)}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Email
                      </label>
                      <input
                        type="email"
                        value={user?.email || ''}
                        disabled
                        className="w-full px-4 py-3 rounded-lg border opacity-50"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-secondary)',
                          color: 'var(--text-secondary)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={user?.phone || ''}
                        onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Location
                      </label>
                      <input
                        type="text"
                        value={user?.location || ''}
                        onChange={(e) => handleProfileUpdate('location', e.target.value)}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                      Bio
                    </label>
                    <textarea
                      value={user?.bio || ''}
                      onChange={(e) => handleProfileUpdate('bio', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                      style={{
                        borderColor: 'var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)'
                      }}
                      placeholder="Tell us about yourself..."
                    />
                  </div>
                </div>
              )}

              {activeSection === 'notifications' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Notification Preferences
                  </h2>
                  
                  <div className="space-y-4">
                    {Object.entries(notifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h3>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setNotifications({ ...notifications, [key]: e.target.checked })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:border after:rounded-full after:h-5 after:w-5 after:transition-all" style={{ backgroundColor: 'var(--border-light)', borderColor: 'var(--border-light)', '--peer-checked-bg': 'var(--primary)', '--after-bg': 'var(--bg-primary)', '--after-border': 'var(--border-light)', '--peer-checked-after-border': 'var(--bg-primary)' } as any}></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={handleNotificationUpdate}
                    disabled={saving}
                    className="flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                  >
                    <Save className="w-5 h-5" />
                    {saving ? 'Saving...' : 'Save Preferences'}
                  </button>
                </div>
              )}

              {activeSection === 'privacy' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Privacy Settings
                  </h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Profile Visibility
                      </label>
                      <select
                        value={privacy.profileVisibility}
                        onChange={(e) => setPrivacy({ ...privacy, profileVisibility: e.target.value })}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      >
                        <option value="public">Public</option>
                        <option value="connections">Connections Only</option>
                        <option value="private">Private</option>
                      </select>
                    </div>

                    {Object.entries(privacy).filter(([key]) => key !== 'profileVisibility').map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h3>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value as boolean}
                            onChange={(e) => setPrivacy({ ...privacy, [key]: e.target.checked })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:border after:rounded-full after:h-5 after:w-5 after:transition-all" style={{ backgroundColor: 'var(--border-light)', borderColor: 'var(--border-light)', '--peer-checked-bg': 'var(--primary)', '--after-bg': 'var(--bg-primary)', '--after-border': 'var(--border-light)', '--peer-checked-after-border': 'var(--bg-primary)' } as any}></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={handlePrivacyUpdate}
                    disabled={saving}
                    className="flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                  >
                    <Save className="w-5 h-5" />
                    {saving ? 'Saving...' : 'Save Settings'}
                  </button>
                </div>
              )}

              {activeSection === 'security' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Security Settings
                  </h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Current Password
                      </label>
                      <input
                        type="password"
                        value={passwordData.currentPassword}
                        onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        New Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          value={passwordData.newPassword}
                          onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                          className="w-full px-4 py-3 pr-12 rounded-lg border focus:ring-2 focus:outline-none"
                          style={{
                            borderColor: 'var(--border-medium)',
                            backgroundColor: 'var(--bg-primary)',
                            color: 'var(--text-primary)'
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showPassword ? (
                            <EyeOff className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
                          ) : (
                            <Eye className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Confirm New Password
                      </label>
                      <input
                        type="password"
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>
                  </div>

                  <button
                    onClick={handlePasswordChange}
                    disabled={saving || !passwordData.newPassword || passwordData.newPassword !== passwordData.confirmPassword}
                    className="flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 disabled:opacity-50"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                  >
                    <Lock className="w-5 h-5" />
                    {saving ? 'Updating...' : 'Update Password'}
                  </button>
                </div>
              )}

              {activeSection === 'preferences' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    App Preferences
                  </h2>
                  
                  <div className="space-y-4">
                    <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-light)' }}>
                      <div className="flex items-center gap-3 mb-2">
                        <AlertCircle className="w-5 h-5" style={{ color: 'var(--warning)' }} />
                        <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                          Coming Soon
                        </h3>
                      </div>
                      <p style={{ color: 'var(--text-secondary)' }}>
                        Additional preference settings will be available in future updates.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
