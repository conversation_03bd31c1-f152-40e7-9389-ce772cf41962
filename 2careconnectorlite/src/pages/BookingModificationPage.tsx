import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Edit3, Calendar as CalendarIcon, User, Clock, MapPin, DollarSign, Save, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { format, parseISO, addDays, isBefore, isAfter } from 'date-fns';

interface BookingModification {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  created_at: string;
  provider_name?: string;
  provider_email?: string;
  provider_phone?: string;
}

const BookingModificationPage: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();
  
  const [booking, setBooking] = useState<BookingModification | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  // Form state
  const [serviceType, setServiceType] = useState('');
  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [location, setLocation] = useState('');
  const [specialRequirements, setSpecialRequirements] = useState('');
  const [hourlyRate, setHourlyRate] = useState(25);

  const serviceTypes = [
    'Personal Care',
    'Companion Care',
    'Medical Assistance', 
    'Housekeeping',
    'Transportation',
    'Meal Preparation',
    'Medication Management',
    'Emergency Care'
  ];

  useEffect(() => {
    const fetchBookingForModification = async () => {
      if (!bookingId) {
        setError("Invalid booking ID.");
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to modify your booking.");
          setLoading(false);
          return;
        }

        // Fetch booking details
        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id)
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          setError("Booking not found or you don't have permission to modify it.");
          setLoading(false);
          return;
        }

        // Check if booking can be modified
        if (['cancelled_by_user', 'cancelled_by_provider', 'completed'].includes(data.status)) {
          setError("This booking cannot be modified due to its current status.");
          setLoading(false);
          return;
        }

        // Check if booking is too close to start time (less than 2 hours)
        const hoursUntilStart = (new Date(data.start_time).getTime() - new Date().getTime()) / (1000 * 60 * 60);
        if (hoursUntilStart < 2) {
          setError("Cannot modify a booking less than 2 hours before the start time.");
          setLoading(false);
          return;
        }

        // Fetch provider details
        const { data: providerData, error: providerError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('full_name, email, phone')
          .eq('id', data.provider_id)
          .single();

        if (providerError) {
          console.warn('Could not fetch provider details:', providerError);
        }

        const bookingWithProvider = {
          ...data,
          provider_name: providerData?.full_name || 'Provider',
          provider_email: providerData?.email || '',
          provider_phone: providerData?.phone || ''
        };

        console.log('Booking for modification fetched successfully:', bookingWithProvider);
        setBooking(bookingWithProvider);

        // Initialize form with current values
        setServiceType(bookingWithProvider.service_type);
        setStartDate(format(parseISO(bookingWithProvider.start_time), 'yyyy-MM-dd'));
        setStartTime(format(parseISO(bookingWithProvider.start_time), 'HH:mm'));
        setEndTime(format(parseISO(bookingWithProvider.end_time), 'HH:mm'));
        setLocation(bookingWithProvider.location || '');
        setSpecialRequirements(bookingWithProvider.special_requirements || '');
        
        // Calculate current hourly rate
        const duration = (new Date(bookingWithProvider.end_time).getTime() - new Date(bookingWithProvider.start_time).getTime()) / (1000 * 60 * 60);
        if (duration > 0) {
          setHourlyRate(Math.round(bookingWithProvider.total_cost / duration));
        }

      } catch (err: any) {
        console.error("Error fetching booking for modification:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingForModification();
  }, [bookingId]);

  const calculateDuration = () => {
    if (!startTime || !endTime) return 0;
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    return Math.max(0, (end.getTime() - start.getTime()) / (1000 * 60 * 60));
  };

  const calculateTotalCost = () => {
    const duration = calculateDuration();
    return duration * hourlyRate;
  };

  const handleSaveModifications = async () => {
    if (!booking || !serviceType || !startDate || !startTime || !endTime) {
      alert('Please fill in all required fields.');
      return;
    }

    if (calculateDuration() <= 0) {
      alert('End time must be after start time.');
      return;
    }

    // Check if the new date is not in the past
    const newStartDateTime = new Date(`${startDate}T${startTime}`);
    if (isBefore(newStartDateTime, new Date())) {
      alert('Cannot schedule booking in the past.');
      return;
    }

    setSaving(true);
    try {
      const newStartDateTime = new Date(`${startDate}T${startTime}`);
      const newEndDateTime = new Date(`${startDate}T${endTime}`);

      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update({ 
          service_type: serviceType,
          start_time: newStartDateTime.toISOString(),
          end_time: newEndDateTime.toISOString(),
          location: location || null,
          special_requirements: specialRequirements || null,
          total_cost: calculateTotalCost(),
          status: 'pending' // Reset to pending after modification
        })
        .eq('id', booking.id);

      if (error) {
        throw error;
      }

      setSaved(true);
      alert('Booking modifications saved successfully!');
      
      // Navigate back to booking details after a short delay
      setTimeout(() => {
        navigate(`/booking/${booking.id}`);
      }, 2000);

    } catch (err: any) {
      console.error('Error saving booking modifications:', err);
      alert('Failed to save modifications. Please try again or contact support.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-logo-green" />
          <span className="text-gray-600 font-medium">Loading booking details...</span>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Cannot Modify Booking</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate('/my-bookings')}
                className="flex-1 bg-gray-100 text-gray-700 px-4 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                My Bookings
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="flex-1 bg-logo-green text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (saved) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-logo-green mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Modifications Saved</h2>
            <p className="text-gray-600 mb-6">Your booking modifications have been saved successfully. Redirecting to booking details...</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="flex-1 bg-logo-green text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                View Booking
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Modify Booking</h1>
                <p className="text-gray-600">Update your appointment details</p>
              </div>
            </div>
            <Edit3 className="h-8 w-8 text-logo-green" />
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Current Booking Details */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Booking Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <div>
              <p className="text-sm text-gray-500 mb-1">Provider</p>
              <p className="font-medium text-gray-900">{booking.provider_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Status</p>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {booking.status.replace(/_/g, ' ').toUpperCase()}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Original Date</p>
                <p className="font-medium text-gray-900">{format(parseISO(booking.start_time), 'MMMM d, yyyy')}</p>
                <p className="text-sm text-gray-500">{format(parseISO(booking.start_time), 'EEEE')}</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Original Time</p>
                <p className="font-medium text-gray-900">
                  {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Current Cost</p>
                <p className="font-medium text-gray-900">${booking.total_cost}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Modification Form */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Modify Booking Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Service Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Service Type *</label>
              <select
                value={serviceType}
                onChange={(e) => setServiceType(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
                required
              >
                <option value="">Select service type</option>
                {serviceTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date *</label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                min={format(addDays(new Date(), 1), 'yyyy-MM-dd')}
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
                required
              />
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Time *</label>
              <input
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
                required
              />
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">End Time *</label>
              <input
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
                required
              />
            </div>

            {/* Hourly Rate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Hourly Rate ($)</label>
              <input
                type="number"
                value={hourlyRate}
                onChange={(e) => setHourlyRate(Math.max(0, parseInt(e.target.value) || 0))}
                min="0"
                step="5"
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>

            {/* Duration & Cost Display */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Duration & New Cost</label>
              <div className="p-3 bg-gray-50 rounded-xl border border-gray-200">
                <p className="text-sm text-gray-600">Duration: {calculateDuration().toFixed(1)} hours</p>
                <p className="text-lg font-semibold text-logo-green">${calculateTotalCost()}</p>
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <input
              type="text"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="Enter service location..."
              className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
            />
          </div>

          {/* Special Requirements */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Special Requirements</label>
            <textarea
              value={specialRequirements}
              onChange={(e) => setSpecialRequirements(e.target.value)}
              placeholder="Any special requirements or notes..."
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-transparent"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
          <div className="flex gap-4">
            <button
              onClick={() => navigate(`/booking/${booking.id}`)}
              className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              disabled={saving}
            >
              Cancel Changes
            </button>
            <button
              onClick={handleSaveModifications}
              disabled={saving || !serviceType || !startDate || !startTime || !endTime || calculateDuration() <= 0}
              className="flex-1 bg-logo-green text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <div className="flex items-center justify-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <Save className="h-4 w-4 mr-2" />
                  Save Modifications
                </div>
              )}
            </button>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Modification Guidelines</h3>
          <ul className="text-gray-600 space-y-1">
            <li>• Modifications must be made at least 2 hours before the original start time</li>
            <li>• Changes may require provider approval and confirmation</li>
            <li>• Cost adjustments will be reflected in your updated booking</li>
            <li>• Contact support if you need assistance with modifications</li>
          </ul>
          <div className="mt-4">
            <Link
              to="/support"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingModificationPage;
