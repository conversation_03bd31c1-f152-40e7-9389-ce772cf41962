import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, History, Calendar as CalendarIcon, User, Clock, MapPin, DollarSign, Search, Filter, Download, Star, ChevronDown, Eye } from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth, subMonths, isWithinInterval } from 'date-fns';

interface BookingHistory {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  created_at: string;
  provider_name?: string;
}

const BookingHistoryPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [bookings, setBookings] = useState<BookingHistory[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<BookingHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Statistics
  const [stats, setStats] = useState({
    totalBookings: 0,
    totalSpent: 0,
    totalHours: 0
  });

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled_by_user', label: 'Cancelled by You' },
    { value: 'cancelled_by_provider', label: 'Cancelled by Provider' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'pending', label: 'Pending' }
  ];

  const timeframeOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'last_3_months', label: 'Last 3 Months' },
    { value: 'this_year', label: 'This Year' }
  ];

  useEffect(() => {
    const fetchBookingHistory = async () => {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view your booking history.");
          setLoading(false);
          return;
        }

        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          setBookings([]);
          setFilteredBookings([]);
          setLoading(false);
          return;
        }

        const bookingsWithProviders = await Promise.all(
          data.map(async (booking) => {
            const { data: providerData } = await supabase
              .schema('care_connector')
              .from('profiles')
              .select('full_name')
              .eq('id', booking.provider_id)
              .single();

            return {
              ...booking,
              provider_name: providerData?.full_name || 'Provider'
            };
          })
        );

        setBookings(bookingsWithProviders);
        setFilteredBookings(bookingsWithProviders);
        calculateStats(bookingsWithProviders);

      } catch (err: any) {
        console.error("Error fetching booking history:", err.message);
        setError(err.message || "Failed to load booking history.");
        setBookings([]);
        setFilteredBookings([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingHistory();
  }, []);

  const calculateStats = (bookings: BookingHistory[]) => {
    const completedBookings = bookings.filter(b => b.status === 'completed');
    
    const totalSpent = completedBookings.reduce((sum, booking) => sum + booking.total_cost, 0);
    const totalHours = completedBookings.reduce((sum, booking) => {
      const duration = (new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60);
      return sum + duration;
    }, 0);

    setStats({
      totalBookings: bookings.length,
      totalSpent,
      totalHours
    });
  };

  const applyFilters = () => {
    let filtered = bookings;

    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.provider_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.service_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.location?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedStatus !== 'all') {
      filtered = filtered.filter(booking => booking.status === selectedStatus);
    }

    if (selectedTimeframe !== 'all') {
      const now = new Date();
      let timeRange;

      switch (selectedTimeframe) {
        case 'this_month':
          timeRange = { start: startOfMonth(now), end: endOfMonth(now) };
          break;
        case 'last_month':
          const lastMonth = subMonths(now, 1);
          timeRange = { start: startOfMonth(lastMonth), end: endOfMonth(lastMonth) };
          break;
        case 'last_3_months':
          timeRange = { start: subMonths(now, 3), end: now };
          break;
        case 'this_year':
          timeRange = { start: new Date(now.getFullYear(), 0, 1), end: now };
          break;
      }

      if (timeRange) {
        filtered = filtered.filter(booking =>
          isWithinInterval(parseISO(booking.start_time), timeRange)
        );
      }
    }

    setFilteredBookings(filtered);
    calculateStats(filtered);
  };

  useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedStatus, selectedTimeframe, bookings]);

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      cancelled_by_user: 'bg-red-100 text-red-800',
      cancelled_by_provider: 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'}`}>
        {status.replace(/_/g, ' ').toUpperCase()}
      </span>
    );
  };

  const exportBookingHistory = () => {
    const headers = ['Date', 'Provider', 'Service', 'Duration', 'Status', 'Cost', 'Location'];
    const csvContent = [
      headers.join(','),
      ...filteredBookings.map(booking => [
        format(parseISO(booking.start_time), 'yyyy-MM-dd'),
        booking.provider_name,
        booking.service_type,
        `${((new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)}h`,
        booking.status,
        `$${booking.total_cost}`,
        booking.location || 'N/A'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `booking-history-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading booking history...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <History className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load History</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => navigate('/dashboard')}
              className="w-full bg-var(--logo-green) text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <History className="h-8 w-8 text-var(--logo-green) mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Booking History</h1>
                <p className="text-gray-600">View and manage your past appointments</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                <ChevronDown className={`h-4 w-4 ml-2 transform transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </button>
              <button
                onClick={exportBookingHistory}
                className="flex items-center px-4 py-2 bg-var(--logo-green) text-white rounded-xl hover:bg-green-600 transition-colors"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-var(--logo-green)" />
              <div className="ml-4">
                <p className="text-sm text-gray-500">Total Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalBookings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-var(--logo-green)" />
              <div className="ml-4">
                <p className="text-sm text-gray-500">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalSpent}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-var(--logo-green)" />
              <div className="ml-4">
                <p className="text-sm text-gray-500">Total Hours</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalHours.toFixed(1)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="rounded-2xl shadow-sm border p-6 mb-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>Filter Bookings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-muted)' }} />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search providers, services..."
                    className="w-full pl-10 pr-4 py-2 border rounded-xl focus:outline-none focus:ring-2"
                    style={{
                      borderColor: 'var(--border-medium)',
                      color: 'var(--text-primary)',
                      backgroundColor: 'var(--bg-primary)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Timeframe</label>
                <select
                  value={selectedTimeframe}
                  onChange={(e) => setSelectedTimeframe(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                >
                  {timeframeOptions.map((option) => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Booking History List */}
        {filteredBookings.length === 0 ? (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-12 text-center">
            <History className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No booking history found</h3>
            <p className="text-gray-600 mb-6">
              {bookings.length === 0 
                ? "You haven't made any bookings yet. Book your first appointment to get started!"
                : "No bookings match your current filters. Try adjusting the search criteria."
              }
            </p>
            <div className="flex gap-3 justify-center">
              <Link
                to="/caregivers"
                className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                Browse Providers
              </Link>
              {bookings.length > 0 && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedStatus('all');
                    setSelectedTimeframe('all');
                  }}
                  className="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                >
                  Clear Filters
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="bg-var(--logo-green) bg-opacity-10 p-3 rounded-xl mr-4">
                      <User className="h-6 w-6 text-var(--logo-green)" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{booking.provider_name}</h3>
                      <p className="text-gray-600">{booking.service_type}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(booking.status)}
                    <Link
                      to={`/booking/${booking.id}`}
                      className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Link>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <p className="font-medium text-gray-900">{format(parseISO(booking.start_time), 'MMM d, yyyy')}</p>
                      <p className="text-gray-500">{format(parseISO(booking.start_time), 'EEEE')}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                      </p>
                      <p className="text-gray-500">
                        {((new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)} hours
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <p className="font-medium text-gray-900">${booking.total_cost}</p>
                      <p className="text-gray-500">Total cost</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <p className="font-medium text-gray-900">{booking.location || 'Location not specified'}</p>
                      <p className="text-gray-500">Service location</p>
                    </div>
                  </div>
                </div>

                {booking.special_requirements && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-xl">
                    <p className="text-sm text-gray-600">
                      <strong>Requirements:</strong> {booking.special_requirements}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/caregivers"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Book New Appointment
            </Link>
            <Link
              to="/my-bookings"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              View Current Bookings
            </Link>
            <Link
              to="/dashboard"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingHistoryPage;
