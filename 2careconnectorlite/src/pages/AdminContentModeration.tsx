import React, { useState, useEffect, useCallback } from 'react';
import { Navigate, Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { formatDistanceToNow } from 'date-fns';

interface ProfileStub {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url?: string | null;
}

interface GroupStub {
  id: string;
  name: string | null;
}

interface FetchedUpdate {
  id: string;
  content: string | null;
  created_at: string;
  user_id: string;
  care_group_id: string;
  author: ProfileStub | null;
  care_groups: GroupStub | null;
}

interface FetchedMessage {
  id: string;
  content: string | null;
  created_at: string;
  user_id: string;
  care_group_id: string;
  author: ProfileStub | null;
  care_groups: GroupStub | null;
}

interface FetchedReview {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  user_id: string;
  is_hidden_by_moderator: boolean | null;
  profiles: ProfileStub | null;
  caregiver_id: string | null;
  facility_id: string | null;
  professional_id: string | null;
  companion_id: string | null;
  care_checker_id: string | null;
  entityType: 'caregiver' | 'facility' | 'professional' | 'companion' | 'care_checker';
}

interface DisplayContent {
  id: string;
  type: 'update' | 'message' | 'review';
  content: string | null;
  created_at: string;
  authorId: string;
  authorName: string;
  groupName?: string;
  is_hidden_by_moderator?: boolean | null;
  entityType?: 'caregiver' | 'facility' | 'professional' | 'companion' | 'care_checker';
}

const AdminContentModeration: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  const [recentUpdates, setRecentUpdates] = useState<FetchedUpdate[]>([]);
  const [recentMessages, setRecentMessages] = useState<FetchedMessage[]>([]);
  const [recentReviews, setRecentReviews] = useState<FetchedReview[]>([]);
  const [contentLoading, setContentLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [activeTab, setActiveTab] = useState('content');
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
      setLoading(false);
    };
    checkAuth();
  }, []);

  // Fetch user profile to check admin status
  useEffect(() => {
    const loadProfile = async () => {
      if (!user) {
        setProfileLoading(false);
        return;
      }
      setProfileLoading(true);
      setProfileError(null);
      
      try {
        const { data, error } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;
        setProfile(data);
      } catch (err: any) {
        setProfileError('Failed to fetch your admin profile. Please try again.');
      } finally {
        setProfileLoading(false);
      }
    };

    if (!loading) {
      loadProfile();
    }
  }, [user, loading]);

  // Fetch content for moderation
  useEffect(() => {
    const fetchContentData = async () => {
      if (!user || !profile?.is_admin) return;
      
      setContentLoading(true);
      setError(null);
      
      try {
        // Fetch recent social posts
        const { data: updates, error: updatesError } = await supabase
          .schema('care_connector')
          .from('social_posts')
          .select(`
            id,
            content,
            created_at,
            user_id,
            care_group_id,
            author:profiles!user_id(id, first_name, last_name, avatar_url),
            care_groups:care_groups!care_group_id(id, name)
          `)
          .order('created_at', { ascending: false })
          .limit(20);

        if (updatesError) throw updatesError;

        // Fetch recent group messages
        const { data: messages, error: messagesError } = await supabase
          .schema('care_connector')
          .from('group_messages')
          .select(`
            id,
            content,
            created_at,
            user_id,
            care_group_id,
            author:profiles!user_id(id, first_name, last_name, avatar_url),
            care_groups:care_groups!care_group_id(id, name)
          `)
          .order('created_at', { ascending: false })
          .limit(20);

        if (messagesError) throw messagesError;

        // Fetch recent reviews (simplified query)
        const { data: reviews, error: reviewsError } = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .select(`
            id,
            rating,
            review_text,
            created_at,
            user_id,
            is_hidden_by_moderator,
            caregiver_id,
            professional_id,
            companion_id,
            care_checker_id,
            profiles!user_id(id, first_name, last_name, avatar_url)
          `)
          .order('created_at', { ascending: false })
          .limit(20);

        if (reviewsError) throw reviewsError;

        setRecentUpdates(updates || []);
        setRecentMessages(messages || []);
        setRecentReviews((reviews || []).map(review => ({
          ...review,
          entityType: review.caregiver_id ? 'caregiver' :
                     review.professional_id ? 'professional' :
                     review.companion_id ? 'companion' :
                     review.care_checker_id ? 'care_checker' : 'caregiver'
        })));
      } catch (err: any) {
        console.error('Error fetching content data:', err);
        setError(err.message);
      } finally {
        setContentLoading(false);
      }
    };

    if (!loading && !profileLoading && profile?.is_admin) {
      fetchContentData();
    }
  }, [user, loading, profileLoading, profile]);

  const handleDeleteItem = async (itemId: string, itemType: 'update' | 'message' | 'review') => {
    setIsDeleting(true);
    setSelectedItemId(itemId);
    
    try {
      const tableName = itemType === 'update' ? 'social_posts' : 
                       itemType === 'message' ? 'group_messages' : 
                       'service_provider_reviews';
      
      const { error } = await supabase
        .schema('care_connector')
        .from(tableName)
        .delete()
        .eq('id', itemId);

      if (error) throw error;

      // Update local state
      if (itemType === 'update') {
        setRecentUpdates(prev => prev.filter(item => item.id !== itemId));
      } else if (itemType === 'message') {
        setRecentMessages(prev => prev.filter(item => item.id !== itemId));
      } else if (itemType === 'review') {
        setRecentReviews(prev => prev.filter(item => item.id !== itemId));
      }
    } catch (err: any) {
      console.error('Error deleting item:', err);
      setError(err.message);
    } finally {
      setIsDeleting(false);
      setSelectedItemId(null);
    }
  };

  const handleToggleReviewVisibility = async (reviewId: string, currentVisibility: boolean | null) => {
    try {
      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_reviews')
        .update({ is_hidden_by_moderator: !currentVisibility })
        .eq('id', reviewId);

      if (error) throw error;

      // Update local state
      setRecentReviews(prev => 
        prev.map(review => 
          review.id === reviewId 
            ? { ...review, is_hidden_by_moderator: !currentVisibility }
            : review
        )
      );
    } catch (err: any) {
      console.error('Error updating review visibility:', err);
      setError(err.message);
    }
  };

  const formatAuthorName = (author: ProfileStub | null): string => {
    if (!author) return 'Unknown User';
    return `${author.first_name || ''} ${author.last_name || ''}`.trim() || 'Anonymous User';
  };

  const renderContentCard = (item: DisplayContent) => (
    <div key={item.id} className="rounded-lg p-6 mb-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-600">
              {item.authorName.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <p className="font-medium text-gray-900">{item.authorName}</p>
            <p className="text-sm text-gray-500">
              {item.groupName && `in ${item.groupName} • `}
              {formatDistanceToNow(new Date(item.created_at))} ago
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {item.type === 'review' && (
            <button
              onClick={() => handleToggleReviewVisibility(item.id, item.is_hidden_by_moderator)}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
            >
              {item.is_hidden_by_moderator ? 'Show' : 'Hide'}
            </button>
          )}
          <button
            onClick={() => handleDeleteItem(item.id, item.type)}
            disabled={isDeleting && selectedItemId === item.id}
            className="px-3 py-1 text-sm rounded disabled:opacity-50 transition-colors"
            style={{ backgroundColor: 'var(--error)', color: 'var(--bg-primary)' }}
            onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--error-dark)')}
            onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--error)')}
          >
            {isDeleting && selectedItemId === item.id ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
      <div className="text-gray-800">
        {item.content || 'No content available'}
      </div>
      {item.type === 'review' && (
        <div className="mt-2 text-sm text-gray-600">
          Entity: {item.entityType}
          {item.is_hidden_by_moderator && (
            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
              Hidden by Moderator
            </span>
          )}
        </div>
      )}
    </div>
  );

  if (loading || profileLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg text-gray-600">Loading content moderation...</div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base text-red-600 leading-relaxed font-medium">{profileError}</div>
      </div>
    );
  }
  
  if (!user || !profile?.is_admin) {
    return <Navigate to="/" />;
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base text-red-600 leading-relaxed font-medium">
          Error loading content data: {error}
        </div>
      </div>
    );
  }

  const allContent: DisplayContent[] = [
    ...recentUpdates.map(update => ({
      id: update.id,
      type: 'update' as const,
      content: update.content,
      created_at: update.created_at,
      authorId: update.user_id,
      authorName: formatAuthorName(update.author),
      groupName: update.care_groups?.name || undefined
    })),
    ...recentMessages.map(message => ({
      id: message.id,
      type: 'message' as const,
      content: message.content,
      created_at: message.created_at,
      authorId: message.user_id,
      authorName: formatAuthorName(message.author),
      groupName: message.care_groups?.name || undefined
    })),
    ...recentReviews.map(review => ({
      id: review.id,
      type: 'review' as const,
      content: review.review_text,
      created_at: review.created_at,
      authorId: review.user_id,
      authorName: formatAuthorName(review.profiles),
      is_hidden_by_moderator: review.is_hidden_by_moderator,
      entityType: review.entityType
    }))
  ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <h1 className="text-3xl font-bold mb-4 tracking-tight text-gray-900">
        Content Moderation
      </h1>
      <p className="text-gray-600 mb-8">
        Monitor and moderate user-generated content across the platform.
      </p>

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('content')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'content'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Recent Content ({allContent.length})
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Reported Content (0)
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'content' && (
        <div>
          {contentLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="animate-pulse">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-300 rounded w-32"></div>
                        <div className="h-3 bg-gray-300 rounded w-24"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : allContent.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-2">No recent content found</div>
              <p className="text-sm text-gray-400">
                User-generated content will appear here for moderation.
              </p>
            </div>
          ) : (
            <div>
              {allContent.map(item => renderContentCard(item))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'reports' && (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-2">No reported content</div>
          <p className="text-sm text-gray-400">
            Reported content will appear here for review.
          </p>
        </div>
      )}
    </div>
  );
};

export default AdminContentModeration;
