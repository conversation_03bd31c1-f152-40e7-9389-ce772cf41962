import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, Filter, MapPin, Clock, DollarSign, Star, Check } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Professionals() {
  const navigate = useNavigate()
  const [professionals, setProfessionals] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [location, setLocation] = useState('')
  const [availability, setAvailability] = useState('Any availability')
  const [maxRate, setMaxRate] = useState(200)
  const [minExperience, setMinExperience] = useState(0)



  useEffect(() => {
    async function fetchProfessionals() {
      try {
        setLoading(true)
        console.log('Fetching professionals from database...')
        // Fetch real professionals from database via dataService
        const data = await dataService.getProfessionals()
        console.log('Professionals data received:', data)
        setProfessionals(data || [])
      } catch (err) {
        console.error('Error loading professionals:', err)
        setProfessionals([]) // Set empty array on error
      } finally {
        setLoading(false)
      }
    }
    fetchProfessionals()
  }, [])

  const handleSearch = () => {
    console.log('Searching with filters:', { searchTerm, location, availability, maxRate, minExperience })
  }

  const resetFilters = () => {
    setSearchTerm('')
    setLocation('')
    setAvailability('Any availability')
    setMaxRate(200)
    setMinExperience(0)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Healthcare Professionals</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding qualified medical professionals for you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header - Elegant & Clean */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>Find Healthcare Professionals</h1>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>{professionals.length} verified professional{professionals.length !== 1 ? 's' : ''} available</p>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar Filters */}
        <div className="w-full lg:w-80 p-4 lg:p-6" style={{ backgroundColor: 'var(--bg-primary)', borderRight: '1px solid var(--border-light)', borderBottom: '1px solid var(--border-light)' }}>
          <div className="flex items-center gap-2 mb-6">
            <Filter className="w-5 h-5" style={{ color: 'var(--primary)' }} />
            <h2 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Search & Filters</h2>
          </div>

          <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }}>
            {/* Search by name */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Search by name</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm"
                style={{
                  border: '1px solid var(--border-light)',
                  backgroundColor: 'var(--bg-secondary)',
                  color: 'var(--text-primary)',
                  fontSize: '14px'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              />
            </div>
          </div>

          {/* Location */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Location</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Enter location..."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm"
                style={{
                  border: '1px solid var(--border-light)',
                  backgroundColor: 'var(--bg-secondary)',
                  color: 'var(--text-primary)',
                  fontSize: '14px'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              />
            </div>
          </div>

          {/* Availability */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Availability</label>
            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 shadow-sm cursor-pointer"
              style={{
                border: '1px solid var(--border-light)',
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                fontSize: '14px'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = 'var(--primary)'
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-light)'
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              <option>Any availability</option>
              <option>Weekdays</option>
              <option>Weekends</option>
              <option>Evenings</option>
            </select>
          </div>

          {/* Max Hourly Rate */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Max Hourly Rate: ${maxRate}</label>
            <input
              type="range"
              min="0"
              max="200"
              value={maxRate}
              onChange={(e) => setMaxRate(Number(e.target.value))}
              className="w-full h-2 rounded-lg appearance-none cursor-pointer"
              style={{
                accentColor: 'var(--primary)',
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(maxRate/200)*100}%, var(--border-light) ${(maxRate/200)*100}%, var(--border-light) 100%)`
              }}
            />
            <div className="flex justify-between text-sm mt-1" style={{ color: 'var(--text-muted)' }}>
              <span>$0</span>
              <span>$200</span>
            </div>
          </div>

          {/* Min Years Experience */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Min Years Experience: {minExperience}</label>
            <input
              type="range"
              min="0"
              max="20"
              value={minExperience}
              onChange={(e) => setMinExperience(Number(e.target.value))}
              className="w-full h-2 rounded-lg appearance-none cursor-pointer"
              style={{
                accentColor: 'var(--primary)',
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(minExperience/20)*100}%, var(--border-light) ${(minExperience/20)*100}%, var(--border-light) 100%)`
              }}
            />
            <div className="flex justify-between text-sm mt-1" style={{ color: 'var(--text-muted)' }}>
              <span>0</span>
              <span>20+</span>
            </div>
          </div>

            {/* Search Button */}
            <button
              type="submit"
              className="w-full py-3 px-6 rounded-lg font-semibold mb-4 transition-colors duration-200 flex items-center justify-center gap-2"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
              }}
            >
              <Search className="w-4 h-4" />
              Search Professionals
            </button>
          </form>

          {/* Reset Filters */}
          <button
            onClick={resetFilters}
            className="w-full py-3 px-6 rounded-lg font-semibold transition-colors duration-200"
            style={{
              backgroundColor: 'var(--bg-secondary)',
              color: 'var(--text-primary)',
              border: '1px solid var(--border-light)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
              e.currentTarget.style.borderColor = 'var(--primary)'
              e.currentTarget.style.transform = 'translateY(-1px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              e.currentTarget.style.borderColor = 'var(--border-light)'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            ↻ Reset Filters
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {professionals.length === 0 ? (
            <div className="text-center py-16">
              <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
                  <Search className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                </div>
                <div>
                  <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>No Healthcare Professionals Available</div>
                  <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Our network is growing - check back soon for new professionals</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {professionals.map((professional: any) => (
                <div
                  key={professional.id}
                  className="group rounded-2xl p-6 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-2xl animate-fadeInUp cursor-pointer"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '2px solid var(--border-light)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
                  }}
                  onClick={() => navigate(`/provider/professionals/${professional.id}`)}
                >
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg ring-4 ring-accent group-hover:ring-primary transition-all duration-300" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <span className="font-bold text-lg" style={{ color: 'var(--primary)' }}>
                          {professional.initials || professional.full_name?.split(' ').map((n: string) => n[0]).join('') || (professional.first_name?.[0] || '') + (professional.last_name?.[0] || '')}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold group-hover:text-primary transition-colors" style={{ color: 'var(--text-primary)' }}>{professional.full_name || `${professional.first_name || ''} ${professional.last_name || ''}`.trim()}</h3>
                        <div className="flex items-center gap-2">
                          {professional.rating && (
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, i) => (
                                <Star 
                                  key={i} 
                                  className={`w-4 h-4 ${
                                    i < Math.floor(professional.rating) 
                                      ? 'text-yellow-400 fill-current' 
                                      : 'text-gray-300'
                                  }`} 
                                />
                              ))}
                              <span className="text-sm text-gray-600 ml-1">{professional.rating}</span>
                            </div>
                          )}
                          {professional.new_provider && (
                            <span className="text-xs px-2 py-1 rounded-full" style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)' }}>New provider</span>
                          )}
                          <span className="text-xs flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                            <span className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></span>
                            Verified
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    {professional.location && (
                      <p className="flex items-center gap-2 mb-2" style={{ color: 'var(--text-secondary)' }}>
                        <MapPin className="w-4 h-4" />
                        {professional.location}
                      </p>
                    )}
                    {professional.needs && (
                      <p className="text-sm" style={{ color: 'var(--text-primary)' }}>
                        {professional.needs}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      {professional.hourly_rate && (
                        <span className="font-semibold flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                          <DollarSign className="w-4 h-4" />
                          ${professional.hourly_rate}/hour
                        </span>
                      )}
                      {professional.years_of_experience && (
                        <span className="text-sm flex items-center gap-1" style={{ color: 'var(--text-secondary)' }}>
                          <Clock className="w-4 h-4" />
                          {professional.years_of_experience} years
                        </span>
                      )}
                    </div>
                  </div>

                  {professional.specialties && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {professional.specialties.slice(0, 2).map((specialty: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 py-1 rounded text-xs"
                            style={{
                              backgroundColor: 'var(--bg-secondary)',
                              color: 'var(--text-primary)'
                            }}
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      navigate(`/provider/professionals/${professional.id}`)
                    }}
                    className="w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary-dark)'
                      e.currentTarget.style.transform = 'translateY(-2px)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary)'
                      e.currentTarget.style.transform = 'translateY(0)'
                    }}
                  >
                    View Profile →
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer
        className="footer"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: 'var(--primary)' }}
              >
                <span className="font-bold text-sm" style={{ color: 'var(--bg-primary)' }}>CC</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Care Connector</h3>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Modern healthcare coordination.</p>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Services</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>Find Care</li>
                  <li>Care Groups</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Learn More</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>How it Works</li>
                  <li>Features</li>
                </ul>
              </div>
            </div>
          </div>
          <div
            className="flex items-center justify-center gap-6 mt-6 pt-6"
            style={{ borderTop: '1px solid var(--border-light)' }}
          >
            <div className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: 'var(--primary)' }}
              ></span>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>HIPAA Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4" style={{ color: 'var(--primary)' }} />
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Verified Providers</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
