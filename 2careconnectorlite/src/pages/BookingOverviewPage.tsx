import React, { useState, useEffect } from 'react'
import { Calendar, Clock, User, DollarSign, TrendingUp, BarChart3, Pie<PERSON>hart, Activity, CheckCircle, AlertCircle, Plus, Filter } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface BookingOverview {
  total_bookings: number
  completed_bookings: number
  pending_bookings: number
  cancelled_bookings: number
  total_spent: number
  this_month_spent: number
  average_session_cost: number
  favorite_providers: Array<{
    provider_name: string
    booking_count: number
    total_spent: number
  }>
  recent_bookings: Array<{
    id: string
    provider_name: string
    service_date: string
    status: string
    total_amount: number
  }>
  upcoming_bookings: Array<{
    id: string
    provider_name: string
    service_date: string
    start_time: string
    total_amount: number
  }>
  monthly_spending: Array<{
    month: string
    amount: number
    booking_count: number
  }>
}

const BookingOverviewPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [overview, setOverview] = useState<BookingOverview | null>(null)
  const [timeRange, setTimeRange] = useState('12') // months
  const [selectedTab, setSelectedTab] = useState('dashboard')

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadBookingOverview()
      }
      setLoading(false)
    }
    getUser()
  }, [timeRange])

  const loadBookingOverview = async () => {
    if (!user) return

    try {
      // Get booking statistics
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id, status, total_amount, service_date, start_time, created_at,
          profiles!bookings_provider_id_fkey(full_name)
        `)
        .eq('client_id', user.id)

      if (bookingsError) throw bookingsError

      const transformedBookings = (bookings || []).map(booking => ({
        id: booking.id,
        status: booking.status,
        total_amount: booking.total_amount,
        service_date: booking.service_date,
        start_time: booking.start_time,
        created_at: booking.created_at,
        provider_name: booking.profiles?.full_name || 'Unknown Provider'
      }))

      // Calculate overview statistics
      const totalBookings = transformedBookings.length
      const completedBookings = transformedBookings.filter(b => b.status === 'completed').length
      const pendingBookings = transformedBookings.filter(b => b.status === 'pending').length
      const cancelledBookings = transformedBookings.filter(b => b.status === 'cancelled').length
      
      const totalSpent = transformedBookings
        .filter(b => b.status === 'completed')
        .reduce((sum, b) => sum + b.total_amount, 0)
      
      const thisMonthStart = new Date()
      thisMonthStart.setDate(1)
      const thisMonthSpent = transformedBookings
        .filter(b => b.status === 'completed' && new Date(b.service_date) >= thisMonthStart)
        .reduce((sum, b) => sum + b.total_amount, 0)
      
      const averageSessionCost = completedBookings > 0 ? totalSpent / completedBookings : 0

      // Get favorite providers
      const providerStats = transformedBookings
        .filter(b => b.status === 'completed')
        .reduce((acc, booking) => {
          const provider = booking.provider_name
          if (!acc[provider]) {
            acc[provider] = { booking_count: 0, total_spent: 0 }
          }
          acc[provider].booking_count++
          acc[provider].total_spent += booking.total_amount
          return acc
        }, {} as Record<string, { booking_count: number; total_spent: number }>)

      const favoriteProviders = Object.entries(providerStats)
        .map(([provider_name, stats]) => ({
          provider_name,
          booking_count: stats.booking_count,
          total_spent: stats.total_spent
        }))
        .sort((a, b) => b.booking_count - a.booking_count)
        .slice(0, 5)

      // Get recent bookings
      const recentBookings = transformedBookings
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
        .map(booking => ({
          id: booking.id,
          provider_name: booking.provider_name,
          service_date: booking.service_date,
          status: booking.status,
          total_amount: booking.total_amount
        }))

      // Get upcoming bookings
      const today = new Date().toISOString().split('T')[0]
      const upcomingBookings = transformedBookings
        .filter(b => b.service_date >= today && (b.status === 'confirmed' || b.status === 'pending'))
        .sort((a, b) => new Date(a.service_date).getTime() - new Date(b.service_date).getTime())
        .slice(0, 5)
        .map(booking => ({
          id: booking.id,
          provider_name: booking.provider_name,
          service_date: booking.service_date,
          start_time: booking.start_time,
          total_amount: booking.total_amount
        }))

      // Calculate monthly spending
      const monthlySpendingMap = transformedBookings
        .filter(b => b.status === 'completed')
        .reduce((acc, booking) => {
          const date = new Date(booking.service_date)
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
          if (!acc[monthKey]) {
            acc[monthKey] = { amount: 0, booking_count: 0 }
          }
          acc[monthKey].amount += booking.total_amount
          acc[monthKey].booking_count++
          return acc
        }, {} as Record<string, { amount: number; booking_count: number }>)

      const monthlySpending = Object.entries(monthlySpendingMap)
        .map(([month, data]) => ({
          month,
          amount: data.amount,
          booking_count: data.booking_count
        }))
        .sort((a, b) => a.month.localeCompare(b.month))
        .slice(-parseInt(timeRange))

      setOverview({
        total_bookings: totalBookings,
        completed_bookings: completedBookings,
        pending_bookings: pendingBookings,
        cancelled_bookings: cancelledBookings,
        total_spent: totalSpent,
        this_month_spent: thisMonthSpent,
        average_session_cost: averageSessionCost,
        favorite_providers: favoriteProviders,
        recent_bookings: recentBookings,
        upcoming_bookings: upcomingBookings,
        monthly_spending: monthlySpending
      })
    } catch (error) {
      console.error('Error loading booking overview:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatMonth = (monthStr: string) => {
    const [year, month] = monthStr.split('-')
    const date = new Date(parseInt(year), parseInt(month) - 1)
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking overview...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <BarChart3 className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Booking Overview Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to view your booking overview and statistics.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  if (!overview) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Unable to Load Overview</h1>
          <p className="text-gray-600">There was an error loading your booking overview.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Overview</h1>
              <p className="text-gray-600">Your complete booking history and statistics</p>
            </div>
            <div className="flex items-center gap-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              >
                <option value="3">Last 3 months</option>
                <option value="6">Last 6 months</option>
                <option value="12">Last 12 months</option>
                <option value="24">Last 2 years</option>
              </select>
              <button
                onClick={() => window.location.href = '/create-booking'}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                New Booking
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
                { id: 'analytics', name: 'Analytics', icon: TrendingUp },
                { id: 'providers', name: 'Top Providers', icon: User }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id)}
                  className={`flex items-center gap-2 py-4 border-b-2 font-medium text-sm ${
                    selectedTab === tab.id
                      ? 'border-logo-green text-logo-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {selectedTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Key Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center gap-4">
                  <Calendar className="w-8 h-8 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{overview.total_bookings}</div>
                    <div className="text-sm text-gray-600">Total Bookings</div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center gap-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{overview.completed_bookings}</div>
                    <div className="text-sm text-gray-600">Completed</div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center gap-4">
                  <DollarSign className="w-8 h-8 text-logo-green" />
                  <div>
                    <div className="text-2xl font-bold text-gray-900">${overview.total_spent.toFixed(2)}</div>
                    <div className="text-sm text-gray-600">Total Spent</div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center gap-4">
                  <Activity className="w-8 h-8 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold text-gray-900">${overview.average_session_cost.toFixed(2)}</div>
                    <div className="text-sm text-gray-600">Avg per Session</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent and Upcoming */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Bookings */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                </div>
                <div className="p-6">
                  {overview.recent_bookings.length > 0 ? (
                    <div className="space-y-4">
                      {overview.recent_bookings.map((booking) => (
                        <div key={booking.id} className="flex justify-between items-center">
                          <div>
                            <div className="font-medium text-gray-900">{booking.provider_name}</div>
                            <div className="text-sm text-gray-600">
                              {new Date(booking.service_date).toLocaleDateString()}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-gray-900">${booking.total_amount.toFixed(2)}</div>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                              {booking.status}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No recent bookings</p>
                  )}
                </div>
              </div>

              {/* Upcoming Bookings */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Upcoming Bookings</h3>
                </div>
                <div className="p-6">
                  {overview.upcoming_bookings.length > 0 ? (
                    <div className="space-y-4">
                      {overview.upcoming_bookings.map((booking) => (
                        <div key={booking.id} className="flex justify-between items-center">
                          <div>
                            <div className="font-medium text-gray-900">{booking.provider_name}</div>
                            <div className="text-sm text-gray-600">
                              {new Date(booking.service_date).toLocaleDateString()} at {booking.start_time}
                            </div>
                          </div>
                          <div className="font-medium text-gray-900">${booking.total_amount.toFixed(2)}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No upcoming bookings</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {selectedTab === 'analytics' && (
          <div className="space-y-6">
            {/* Monthly Spending Chart */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Spending Trend</h3>
              <div className="space-y-4">
                {overview.monthly_spending.map((month) => (
                  <div key={month.month} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-900">{formatMonth(month.month)}</span>
                        <span className="text-sm text-gray-600">${month.amount.toFixed(2)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-logo-green h-2 rounded-full"
                          style={{ 
                            width: overview.monthly_spending.length > 0 
                              ? `${(month.amount / Math.max(...overview.monthly_spending.map(m => m.amount))) * 100}%`
                              : '0%'
                          }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">{month.booking_count} bookings</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Booking Status Distribution */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Booking Status Distribution</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{overview.completed_bookings}</div>
                  <div className="text-sm text-gray-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{overview.pending_bookings}</div>
                  <div className="text-sm text-gray-600">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{overview.cancelled_bookings}</div>
                  <div className="text-sm text-gray-600">Cancelled</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{overview.total_bookings}</div>
                  <div className="text-sm text-gray-600">Total</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Top Providers Tab */}
        {selectedTab === 'providers' && (
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Your Top Care Providers</h3>
            </div>
            <div className="p-6">
              {overview.favorite_providers.length > 0 ? (
                <div className="space-y-4">
                  {overview.favorite_providers.map((provider, index) => (
                    <div key={provider.provider_name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-logo-green text-white rounded-full flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{provider.provider_name}</div>
                          <div className="text-sm text-gray-600">{provider.booking_count} bookings completed</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-gray-900">${provider.total_spent.toFixed(2)}</div>
                        <div className="text-sm text-gray-600">total spent</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No provider data available</p>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button
              onClick={() => window.location.href = '/create-booking'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Plus className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">New Booking</div>
              <div className="text-sm text-gray-600">Schedule a new appointment</div>
            </button>
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">All Bookings</div>
              <div className="text-sm text-gray-600">View all your bookings</div>
            </button>
            <button
              onClick={() => window.location.href = '/booking-recurring'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Activity className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Recurring Bookings</div>
              <div className="text-sm text-gray-600">Manage recurring appointments</div>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <User className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Dashboard</div>
              <div className="text-sm text-gray-600">Return to main dashboard</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingOverviewPage
