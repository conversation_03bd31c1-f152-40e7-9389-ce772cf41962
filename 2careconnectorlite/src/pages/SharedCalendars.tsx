import React, { useState } from 'react'
import { Calendar, Plus, Clock, Users, Bell } from 'lucide-react'

export default function SharedCalendars() {
  const [selectedDate, setSelectedDate] = useState(new Date())
  
  const appointments = [
    {
      id: 1,
      title: "<PERSON><PERSON> <PERSON> Appointment",
      time: "10:00 AM",
      duration: "1 hour",
      type: "Medical",
      attendees: ["Patient", "<PERSON><PERSON> <PERSON>"],
      color: "bg-blue-500"
    },
    {
      id: 2,
      title: "Physical Therapy",
      time: "2:00 PM", 
      duration: "45 mins",
      type: "Therapy",
      attendees: ["Patient", "Therapist"],
      color: "bg-green-500"
    },
    {
      id: 3,
      title: "Medication Review",
      time: "4:30 PM",
      duration: "30 mins", 
      type: "Care",
      attendees: ["Patient", "Caregiver"],
      color: "bg-purple-500"
    }
  ]

  const upcomingEvents = [
    { date: "Tomorrow", event: "Blood Test", time: "9:00 AM" },
    { date: "Friday", event: "Cardiology Check-up", time: "11:30 AM" },
    { date: "Next Week", event: "Physical Therapy", time: "2:00 PM" }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Shared Calendars</h1>
              <p className="mt-2 text-gray-600">Coordinate care schedules with your team</p>
            </div>
            <button className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center">
              <Plus className="w-4 h-4 mr-2" />
              New Appointment
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Calendar View */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {selectedDate.toLocaleDateString('en-US', { 
                    month: 'long', 
                    year: 'numeric' 
                  })}
                </h2>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 text-sm bg-gray-100 rounded-md hover:bg-gray-200">
                    Week
                  </button>
                  <button className="px-3 py-1 text-sm bg-teal-100 text-teal-700 rounded-md">
                    Month
                  </button>
                </div>
              </div>

              {/* Today's Schedule */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Today's Schedule
                </h3>
                <div className="space-y-3">
                  {appointments.map((appointment) => (
                    <div key={appointment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start">
                          <div className={`w-3 h-3 rounded-full ${appointment.color} mt-2 mr-3`}></div>
                          <div>
                            <h4 className="font-medium text-gray-900">{appointment.title}</h4>
                            <div className="flex items-center text-sm text-gray-600 mt-1">
                              <Clock className="w-4 h-4 mr-1" />
                              {appointment.time} ({appointment.duration})
                            </div>
                            <div className="flex items-center text-sm text-gray-600 mt-1">
                              <Users className="w-4 h-4 mr-1" />
                              {appointment.attendees.join(', ')}
                            </div>
                          </div>
                        </div>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {appointment.type}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
                <div className="flex space-x-3">
                  <button className="text-sm bg-teal-50 text-teal-700 px-3 py-2 rounded-md hover:bg-teal-100">
                    Schedule Appointment
                  </button>
                  <button className="text-sm bg-blue-50 text-blue-700 px-3 py-2 rounded-md hover:bg-blue-100">
                    Set Reminder
                  </button>
                  <button className="text-sm bg-purple-50 text-purple-700 px-3 py-2 rounded-md hover:bg-purple-100">
                    Recurring Event
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Events */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Bell className="w-5 h-5 mr-2" />
                Upcoming Events
              </h3>
              <div className="space-y-3">
                {upcomingEvents.map((event, index) => (
                  <div key={index} className="border-l-4 border-teal-500 pl-3">
                    <p className="font-medium text-gray-900">{event.event}</p>
                    <p className="text-sm text-gray-600">{event.date} at {event.time}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Calendar Legend */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Calendar Types</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <span className="text-sm text-gray-700">Medical Appointments</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="text-sm text-gray-700">Therapy Sessions</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                  <span className="text-sm text-gray-700">Care Activities</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                  <span className="text-sm text-gray-700">Medication Reminders</span>
                </div>
              </div>
            </div>

            {/* Care Team */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Care Team Access</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Dr. Smith</span>
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">Active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Physical Therapist</span>
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">Active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Primary Caregiver</span>
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">Active</span>
                </div>
              </div>
              <button className="w-full mt-3 text-sm text-teal-600 hover:text-teal-700">
                Manage Permissions
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
