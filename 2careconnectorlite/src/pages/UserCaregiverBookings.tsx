import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Calendar as CalendarIcon, User, MessageSquare, Eye, Filter, Search, Clock, MapPin, DollarSign } from 'lucide-react';
import { format, parseISO, isAfter, isBefore } from 'date-fns';

interface CaregiverBooking {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  cancellation_reason?: string;
  user_email?: string;
  client_name?: string;
  location?: string;
  created_at: string;
}

const UserCaregiverBookings: React.FC = () => {
  const navigate = useNavigate();
  
  const [bookings, setBookings] = useState<CaregiverBooking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'past' | 'pending' | 'cancelled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchCaregiverBookings = async () => {
      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view your caregiver bookings.");
          setLoading(false);
          return;
        }

        // Fetch bookings where current user is the caregiver/provider
        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('provider_id', user.id)
          .eq('provider_type', 'caregiver')
          .order('created_at', { ascending: false });

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        console.log('Caregiver bookings data fetched successfully:', data);
        setBookings(data || []);
      } catch (err: any) {
        console.error("Error fetching caregiver bookings:", err.message);
        setError(err.message || "Failed to load caregiver bookings.");
        setBookings([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCaregiverBookings();
  }, []);

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-var(--logo-green) text-white';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled_by_user':
      case 'cancelled_by_provider':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending Approval';
      case 'cancelled_by_user':
        return 'Cancelled by Client';
      case 'cancelled_by_provider':
        return 'Cancelled by You';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  const getFilteredBookings = () => {
    let filtered = bookings;

    // Apply status filter
    if (filter === 'upcoming') {
      filtered = filtered.filter(booking => 
        isAfter(parseISO(booking.start_time), new Date()) && 
        !['cancelled_by_user', 'cancelled_by_provider', 'completed'].includes(booking.status)
      );
    } else if (filter === 'past') {
      filtered = filtered.filter(booking => 
        isBefore(parseISO(booking.start_time), new Date()) || 
        booking.status === 'completed'
      );
    } else if (filter === 'pending') {
      filtered = filtered.filter(booking => booking.status === 'pending');
    } else if (filter === 'cancelled') {
      filtered = filtered.filter(booking => 
        ['cancelled_by_user', 'cancelled_by_provider'].includes(booking.status)
      );
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.service_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (booking.client_name && booking.client_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (booking.location && booking.location.toLowerCase().includes(searchTerm.toLowerCase())) ||
        booking.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  const handleAcceptBooking = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update({ status: 'confirmed' })
        .eq('id', bookingId);

      if (error) {
        throw error;
      }

      // Update local state
      setBookings(prev => 
        prev.map(booking => 
          booking.id === bookingId 
            ? { ...booking, status: 'confirmed' }
            : booking
        )
      );

      alert('Booking accepted successfully!');
    } catch (err: any) {
      console.error('Error accepting booking:', err);
      alert('Failed to accept booking. Please try again.');
    }
  };

  const handleDeclineBooking = async (bookingId: string) => {
    const reason = prompt('Please provide a reason for declining this booking:');
    if (!reason) return;

    try {
      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update({ 
          status: 'cancelled_by_provider',
          cancellation_reason: reason
        })
        .eq('id', bookingId);

      if (error) {
        throw error;
      }

      // Update local state
      setBookings(prev => 
        prev.map(booking => 
          booking.id === bookingId 
            ? { ...booking, status: 'cancelled_by_provider', cancellation_reason: reason }
            : booking
        )
      );

      alert('Booking declined successfully.');
    } catch (err: any) {
      console.error('Error declining booking:', err);
      alert('Failed to decline booking. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading your caregiver bookings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Bookings</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-var(--logo-green) text-white px-6 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  const filteredBookings = getFilteredBookings();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Caregiver Bookings</h1>
              <p className="text-gray-600">Manage booking requests and appointments from clients</p>
            </div>
            <Link
              to="/dashboard"
              className="flex items-center px-6 py-3 bg-var(--logo-green) text-white rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              <User className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Filters and Search */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search bookings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                />
              </div>
            </div>

            {/* Filter Buttons */}
            <div className="flex gap-2">
              {(['all', 'pending', 'upcoming', 'past', 'cancelled'] as const).map((filterOption) => (
                <button
                  key={filterOption}
                  onClick={() => setFilter(filterOption)}
                  className={`px-4 py-3 rounded-xl font-medium transition-colors capitalize ${
                    filter === filterOption
                      ? 'bg-var(--logo-green) text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filterOption}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bookings List */}
        {filteredBookings.length === 0 ? (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-12">
            <div className="text-center">
              <CalendarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {bookings.length === 0 ? 'No Bookings Yet' : 'No Matching Bookings'}
              </h3>
              <p className="text-gray-600 mb-6">
                {bookings.length === 0 
                  ? 'You haven\'t received any booking requests yet.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  
                  {/* Booking Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="h-12 w-12 rounded-full bg-var(--logo-green) bg-opacity-10 flex items-center justify-center">
                        <User className="h-6 w-6 text-var(--logo-green)" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{booking.service_type}</h3>
                        <p className="text-gray-600">
                          Client: {booking.client_name || booking.user_email || 'Anonymous'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm mb-4">
                      <div className="flex items-center text-gray-600">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        <div>
                          <p className="font-medium text-gray-900">
                            {format(parseISO(booking.start_time), 'MMM d, yyyy')}
                          </p>
                          <p>{format(parseISO(booking.start_time), 'EEEE')}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center text-gray-600">
                        <Clock className="mr-2 h-4 w-4" />
                        <div>
                          <p className="font-medium text-gray-900">
                            {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                          </p>
                          <p>
                            {((new Date(booking.end_time).getTime() - new Date(booking.start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)}h duration
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center text-gray-600">
                        <DollarSign className="mr-2 h-4 w-4" />
                        <div>
                          <p className="font-medium text-gray-900">${booking.total_cost}</p>
                          <p>Total payment</p>
                        </div>
                      </div>
                    </div>
                    
                    {booking.location && (
                      <div className="flex items-center text-gray-600 text-sm mb-4">
                        <MapPin className="mr-2 h-4 w-4" />
                        <span>{booking.location}</span>
                      </div>
                    )}
                    
                    {booking.special_requirements && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-500 mb-1">Special Requirements:</p>
                        <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{booking.special_requirements}</p>
                      </div>
                    )}
                    
                    {booking.cancellation_reason && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-500 mb-1">Cancellation Reason:</p>
                        <p className="text-sm text-gray-900 bg-red-50 p-3 rounded-lg">{booking.cancellation_reason}</p>
                      </div>
                    )}
                  </div>

                  {/* Status and Actions */}
                  <div className="flex flex-col items-end gap-3 ml-6">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(booking.status)}`}>
                      {getStatusLabel(booking.status)}
                    </div>
                    
                    <div className="flex flex-col gap-2">
                      <Link
                        to={`/booking/${booking.id}`}
                        className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors text-sm"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                      
                      {booking.status === 'pending' && (
                        <div className="flex flex-col gap-2">
                          <button
                            onClick={() => handleAcceptBooking(booking.id)}
                            className="flex items-center px-4 py-2 bg-var(--logo-green) text-white rounded-lg font-medium hover:bg-green-600 transition-colors text-sm"
                          >
                            Accept
                          </button>
                          <button
                            onClick={() => handleDeclineBooking(booking.id)}
                            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors text-sm"
                          >
                            Decline
                          </button>
                        </div>
                      )}
                      
                      {booking.status === 'confirmed' && (
                        <Link
                          to={`/messages/new?recipientId=${booking.user_id}&clientName=${encodeURIComponent(booking.client_name || 'Client')}`}
                          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors text-sm"
                        >
                          <MessageSquare className="mr-2 h-4 w-4" />
                          Message
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        {bookings.length > 0 && (
          <div className="mt-8 bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Caregiver Stats</h3>
            <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-var(--logo-green)">{bookings.length}</p>
                <p className="text-sm text-gray-600">Total Bookings</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">
                  {bookings.filter(b => b.status === 'pending').length}
                </p>
                <p className="text-sm text-gray-600">Pending</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {bookings.filter(b => b.status === 'confirmed').length}
                </p>
                <p className="text-sm text-gray-600">Confirmed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {bookings.filter(b => b.status === 'completed').length}
                </p>
                <p className="text-sm text-gray-600">Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-600">
                  ${bookings.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.total_cost, 0)}
                </p>
                <p className="text-sm text-gray-600">Total Earned</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserCaregiverBookings;
