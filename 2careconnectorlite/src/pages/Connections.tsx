import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { Users, UserPlus, MessageCircle, Phone, Video, MoreHorizontal, Search } from 'lucide-react'

interface Connection {
  id: string
  user_id: string
  connected_user_id: string
  status: 'pending' | 'accepted' | 'blocked'
  created_at: string
  updated_at: string
  connected_user?: {
    id: string
    email: string
    full_name: string | null
    profile_image_url: string | null
    role: string
    location?: string
  }
}

interface ConnectionRequest {
  id: string
  requester_id: string
  requested_user_id: string
  status: 'pending' | 'accepted' | 'declined'
  message?: string
  created_at: string
  requester?: {
    id: string
    email: string
    full_name: string | null
    profile_image_url: string | null
    role: string
  }
}

const Connections: React.FC = () => {
  const [connections, setConnections] = useState<Connection[]>([])
  const [connectionRequests, setConnectionRequests] = useState<ConnectionRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState<'connections' | 'requests'>('connections')

  useEffect(() => {
    getCurrentUser()
  }, [])

  useEffect(() => {
    if (currentUser) {
      loadConnections()
      loadConnectionRequests()
    }
  }, [currentUser])

  const getCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single()
        
        setCurrentUser(profile)
      }
    } catch (error) {
      console.error('Error getting current user:', error)
    }
  }

  const loadConnections = async () => {
    if (!currentUser) return

    try {
      const { data, error } = await supabase
        .from('connections')
        .select(`
          *,
          connected_user:connected_user_id(
            id, email, full_name, profile_image_url, role, location
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'accepted')
        .order('created_at', { ascending: false })

      if (error) throw error
      setConnections(data || [])
    } catch (error) {
      console.error('Error loading connections:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadConnectionRequests = async () => {
    if (!currentUser) return

    try {
      const { data, error } = await supabase
        .from('connection_requests')
        .select(`
          *,
          requester:requester_id(
            id, email, full_name, profile_image_url, role
          )
        `)
        .eq('requested_user_id', currentUser.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })

      if (error) throw error
      setConnectionRequests(data || [])
    } catch (error) {
      console.error('Error loading connection requests:', error)
    }
  }

  const handleAcceptRequest = async (requestId: string) => {
    try {
      const { error } = await supabase
        .from('connection_requests')
        .update({ status: 'accepted' })
        .eq('id', requestId)

      if (error) throw error
      
      // Reload data
      loadConnectionRequests()
      loadConnections()
    } catch (error) {
      console.error('Error accepting request:', error)
    }
  }

  const handleDeclineRequest = async (requestId: string) => {
    try {
      const { error } = await supabase
        .from('connection_requests')
        .update({ status: 'declined' })
        .eq('id', requestId)

      if (error) throw error
      loadConnectionRequests()
    } catch (error) {
      console.error('Error declining request:', error)
    }
  }

  const filteredConnections = connections.filter(connection =>
    connection.connected_user?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    connection.connected_user?.email?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header */}
      <div className="px-6 py-8" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
                My Connections
              </h1>
              <p className="text-lg mt-2" style={{ color: 'var(--text-secondary)' }}>
                Manage your healthcare network and connections
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)'
                }}
              >
                <UserPlus className="w-5 h-5" />
                Find People
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search connections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg border focus:ring-2 focus:outline-none"
              style={{
                borderColor: 'var(--border-medium)',
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)'
              }}
            />
          </div>

          {/* Tabs */}
          <div className="flex gap-1 p-1 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
            <button
              onClick={() => setActiveTab('connections')}
              className={`flex-1 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                activeTab === 'connections' ? 'shadow-sm' : ''
              }`}
              style={{
                backgroundColor: activeTab === 'connections' ? 'var(--bg-primary)' : 'transparent',
                color: activeTab === 'connections' ? 'var(--text-primary)' : 'var(--text-secondary)'
              }}
            >
              Connections ({connections.length})
            </button>
            <button
              onClick={() => setActiveTab('requests')}
              className={`flex-1 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                activeTab === 'requests' ? 'shadow-sm' : ''
              }`}
              style={{
                backgroundColor: activeTab === 'requests' ? 'var(--bg-primary)' : 'transparent',
                color: activeTab === 'requests' ? 'var(--text-primary)' : 'var(--text-secondary)'
              }}
            >
              Requests ({connectionRequests.length})
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
          </div>
        ) : (
          <>
            {activeTab === 'connections' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredConnections.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <Users className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                    <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                      No connections yet
                    </h3>
                    <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                      Start building your healthcare network by connecting with providers and other users.
                    </p>
                  </div>
                ) : (
                  filteredConnections.map((connection) => (
                    <div
                      key={connection.id}
                      className="p-6 rounded-lg border transition-all duration-200 hover:shadow-md"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        borderColor: 'var(--border-light)'
                      }}
                    >
                      <div className="flex items-center gap-4 mb-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: 'var(--bg-accent)' }}
                        >
                          <Users className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {connection.connected_user?.full_name || connection.connected_user?.email}
                          </h3>
                          <p className="text-sm capitalize" style={{ color: 'var(--text-secondary)' }}>
                            {connection.connected_user?.role?.replace('_', ' ')}
                          </p>
                        </div>
                        <button className="p-2 rounded-lg hover:bg-opacity-80 transition-colors">
                          <MoreHorizontal className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
                        </button>
                      </div>

                      <div className="flex gap-2">
                        <button
                          className="flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-lg font-medium transition-all duration-200"
                          style={{
                            backgroundColor: 'var(--bg-secondary)',
                            color: 'var(--text-primary)'
                          }}
                        >
                          <MessageCircle className="w-4 h-4" />
                          Message
                        </button>
                        <button
                          className="px-3 py-2 rounded-lg transition-all duration-200"
                          style={{
                            backgroundColor: 'var(--bg-secondary)',
                            color: 'var(--text-primary)'
                          }}
                        >
                          <Phone className="w-4 h-4" />
                        </button>
                        <button
                          className="px-3 py-2 rounded-lg transition-all duration-200"
                          style={{
                            backgroundColor: 'var(--bg-secondary)',
                            color: 'var(--text-primary)'
                          }}
                        >
                          <Video className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'requests' && (
              <div className="space-y-4">
                {connectionRequests.length === 0 ? (
                  <div className="text-center py-12">
                    <UserPlus className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                    <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                      No pending requests
                    </h3>
                    <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                      You don't have any pending connection requests at the moment.
                    </p>
                  </div>
                ) : (
                  connectionRequests.map((request) => (
                    <div
                      key={request.id}
                      className="p-6 rounded-lg border"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        borderColor: 'var(--border-light)'
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div
                            className="w-12 h-12 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: 'var(--bg-accent)' }}
                          >
                            <Users className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                          </div>
                          <div>
                            <h3 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                              {request.requester?.full_name || request.requester?.email}
                            </h3>
                            <p className="text-sm capitalize" style={{ color: 'var(--text-secondary)' }}>
                              {request.requester?.role?.replace('_', ' ')} • {new Date(request.created_at).toLocaleDateString()}
                            </p>
                            {request.message && (
                              <p className="text-sm mt-2" style={{ color: 'var(--text-primary)' }}>
                                "{request.message}"
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleAcceptRequest(request.id)}
                            className="px-4 py-2 rounded-lg font-medium transition-all duration-200"
                            style={{
                              backgroundColor: 'var(--primary)',
                              color: 'var(--bg-primary)'
                            }}
                          >
                            Accept
                          </button>
                          <button
                            onClick={() => handleDeclineRequest(request.id)}
                            className="px-4 py-2 rounded-lg font-medium transition-all duration-200"
                            style={{
                              backgroundColor: 'var(--bg-secondary)',
                              color: 'var(--text-primary)',
                              border: '1px solid var(--border-medium)'
                            }}
                          >
                            Decline
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default Connections
