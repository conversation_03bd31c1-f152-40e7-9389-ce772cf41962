import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Search, Calendar, Users, FileText, MessageSquare, Star } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function HowItWorks() {
  // Static content for How It Works page (could be moved to database later)
  const staticSteps = [
    {
      step_number: 1,
      step_title: "Find Your Perfect Care Match",
      step_description: "Browse verified caregivers, companions, and healthcare professionals in your area.",
      icon_name: "Search",
      features_json: '["Background-checked providers", "Verified credentials", "Real reviews"]',
      action_text: "Browse Caregivers",
      action_link: "/caregivers"
    },
    {
      step_number: 2,
      step_title: "Schedule & Coordinate Care",
      step_description: "Book appointments, coordinate schedules, and manage care plans seamlessly.",
      icon_name: "Calendar",
      features_json: '["Smart scheduling", "Automated reminders", "Family coordination"]',
      action_text: "Get Started",
      action_link: "/get-started"
    },
    {
      step_number: 3,
      step_title: "Stay Connected & Informed",
      step_description: "Communicate securely with your care team and track progress in real-time.",
      icon_name: "MessageSquare",
      features_json: '["HIPAA-compliant messaging", "Progress tracking", "Care updates"]',
      action_text: "Learn More",
      action_link: "/features"
    }
  ]

  const [steps] = useState(staticSteps)
  const [loading] = useState(false)

  // Icon mapping for static content
  const iconMap: Record<string, any> = {
    'Search': Search,
    'Calendar': Calendar,
    'Users': Users,
    'FileText': FileText,
    'MessageSquare': MessageSquare,
    'Star': Star
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl mb-6">
            How <span style={{color: 'var(--primary)'}}>Care Connector</span> Works
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
            Connect with caregivers, coordinate with family, and manage care seamlessly.
          </p>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="text-center mb-16">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2" style={{borderColor: 'var(--primary)'}}></div>
            <p className="mt-2 text-gray-600">Loading how it works...</p>
          </div>
        ) : (
          /* Steps - Dynamic from database */
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {steps.map((step) => {
              const Icon = iconMap[step.icon_name] || Search // Fallback to Search icon
              const features = step.features_json ? JSON.parse(step.features_json) : []
              
              return (
                <div key={step.step_number} className="bg-white rounded-lg p-6 shadow-lg relative">
                  {/* Step Number */}
                  <div className="absolute -top-3 -right-3 w-8 h-8 text-white rounded-full flex items-center justify-center text-sm font-bold" style={{backgroundColor: 'var(--primary)'}}>
                    {step.step_number}
                  </div>
                  
                  {/* Icon */}
                  <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{backgroundColor: 'var(--bg-accent)'}}>
                    <Icon className="w-8 h-8" style={{color: 'var(--primary)'}} />
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 text-center">
                    {step.step_title}
                  </h3>
                  <p className="text-gray-600 mb-4 text-center text-sm">
                    {step.step_description}
                  </p>
                  
                  {/* Features */}
                  {features.length > 0 && (
                    <ul className="text-sm text-gray-600 mb-4 space-y-1">
                      {features.map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2" style={{color: 'var(--primary)'}}>✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  )}
                  
                  {/* Action Button */}
                  {step.action_link && step.action_text && (
                    <Link 
                      to={step.action_link}
                      className="block w-full text-center bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded text-sm font-medium transition-colors"
                    >
                      {step.action_text} →
                    </Link>
                  )}
                </div>
              )
            })}
          </div>
        )}
        
        {/* CTA Section */}
        <div className="text-center bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Take <span style={{color: 'var(--primary)'}}>Control</span>?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Transform how you coordinate care with confidence, clarity, and support.
          </p>
          <div className="space-x-4">
            <Link
              to="/get-started"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white transition-colors" style={{backgroundColor: 'var(--primary)'}} onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--primary-dark)'} onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--primary)'}
            >
              Get Started Free
            </Link>
            <Link
              to="/caregivers"
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Browse Caregivers
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
