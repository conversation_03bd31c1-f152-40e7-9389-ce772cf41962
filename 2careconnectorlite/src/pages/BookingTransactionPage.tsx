import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON> } from 'react-router-dom'
import { CreditCard, Download, ArrowUpRight, ArrowDownLeft, DollarSign, Check, Clock, AlertCircle, Search, Lock } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface Transaction {
  id: string
  booking_id: string
  transaction_type: 'payment' | 'refund' | 'fee' | 'adjustment'
  amount: number
  fee_amount: number
  net_amount: number
  payment_method: 'card' | 'bank_transfer' | 'wallet' | 'cash'
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded'
  transaction_date: string
  reference_number: string
  description: string
  booking_info?: {
    provider_name: string
    service_date: string
  }
}

const BookingTransactionPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')

  const [stats, setStats] = useState({
    total_payments: 0,
    total_refunds: 0,
    pending_amount: 0
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    getUser()
  }, [])

  const loadTransactions = useCallback(async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('transactions')
        .select(`
          id, booking_id, transaction_type, amount, fee_amount, net_amount, 
          payment_method, status, transaction_date, reference_number, description,
          bookings!transactions_booking_id_fkey(
            service_date, profiles!bookings_provider_id_fkey(full_name)
          )
        `)
        .eq('user_id', user.id)
        .order('transaction_date', { ascending: false })

      if (error) throw error

      const transformed = (data || []).map(t => ({
        id: t.id,
        booking_id: t.booking_id,
        transaction_type: t.transaction_type,
        amount: t.amount,
        fee_amount: t.fee_amount || 0,
        net_amount: t.net_amount,
        payment_method: t.payment_method,
        status: t.status,
        transaction_date: t.transaction_date,
        reference_number: t.reference_number,
        description: t.description,
        booking_info: t.bookings && t.bookings[0] ? {
          provider_name: t.bookings[0].profiles?.[0]?.full_name || 'Unknown Provider',
          service_date: t.bookings[0].service_date
        } : undefined
      }))

      setTransactions(transformed)

      const payments = transformed.filter(t => t.transaction_type === 'payment' && t.status === 'completed')
      const refunds = transformed.filter(t => t.transaction_type === 'refund' && t.status === 'completed')
      const pending = transformed.filter(t => t.status === 'pending')

      setStats({
        total_payments: payments.reduce((sum, t) => sum + t.amount, 0),
        total_refunds: refunds.reduce((sum, t) => sum + t.amount, 0),
        pending_amount: pending.reduce((sum, t) => sum + t.amount, 0)
      })
    } catch (error) {
      console.error('Error loading transactions:', error)
    }
  }, [user])

  useEffect(() => {
    if (user) {
      loadTransactions()
    }
  }, [user, loadTransactions])

  const downloadReceipt = (transaction: Transaction) => {
    const content = `CARE CONNECTOR RECEIPT\n\nID: ${transaction.reference_number}\nDate: ${new Date(transaction.transaction_date).toLocaleDateString()}\nType: ${transaction.transaction_type}\nAmount: $${transaction.amount.toFixed(2)}\nStatus: ${transaction.status}\n\nProvider: ${transaction.booking_info?.provider_name || 'N/A'}\nService Date: ${transaction.booking_info?.service_date ? new Date(transaction.booking_info.service_date).toLocaleDateString() : 'N/A'}`
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `receipt-${transaction.reference_number}.txt`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const getIcon = (type: string) => {
    switch (type) {
      case 'payment': return <ArrowUpRight className="w-4 h-4 text-red-600" />
      case 'refund': return <ArrowDownLeft className="w-4 h-4 text-green-600" />
      default: return <DollarSign className="w-4 h-4 text-orange-600" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <Check className="w-4 h-4 text-green-600" />
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />
      default: return <AlertCircle className="w-4 h-4 text-red-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-red-100 text-red-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment': return 'bg-red-100 text-red-800'
      case 'refund': return 'bg-green-100 text-green-800'
      default: return 'bg-orange-100 text-orange-800'
    }
  }

  const filteredTransactions = transactions.filter(t => {
    const matchesSearch = searchTerm === '' || 
      t.reference_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      t.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      t.booking_info?.provider_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === 'all' || t.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: 'var(--primary)' }}></div>
          <p className="macos-body" style={{ color: 'var(--text-secondary)' }}>Loading transactions...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        {/* Header with Navigation */}
        <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold macos-title" style={{ color: 'var(--text-primary)' }}>Transaction Management</h1>
                <p className="mt-1 macos-body" style={{ color: 'var(--text-secondary)' }}>View and manage your booking transactions and payments</p>
              </div>
              <Link
                to="/dashboard"
                className="transition-colors macos-body"
                style={{ color: 'var(--text-secondary)' }}
                onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Authentication Error State */}
        <div className="flex items-center justify-center flex-1 py-16">
          <div className="text-center max-w-md mx-auto p-8">
            <Lock className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--error)' }} />
            <h2 className="text-2xl font-bold mb-2 macos-title" style={{ color: 'var(--text-primary)' }}>Access Restricted</h2>
            <p className="mb-6 macos-body" style={{ color: 'var(--text-secondary)' }}>Please sign in to view and manage your booking transactions and payments.</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => window.location.href = '/auth'}
                className="px-6 py-3 rounded-lg transition-colors macos-body"
                style={{ backgroundColor: 'var(--primary)', color: 'var(--text-white)' }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                Sign In
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="px-6 py-3 rounded-lg border transition-colors macos-body"
                style={{ 
                  borderColor: 'var(--border-medium)', 
                  backgroundColor: 'var(--bg-primary)', 
                  color: 'var(--text-primary)' 
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Transaction History</h1>
          <p className="text-gray-600">View and manage your booking transactions and payments</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <ArrowUpRight className="w-8 h-8 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${stats.total_payments.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Payments</div>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <ArrowDownLeft className="w-8 h-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${stats.total_refunds.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Refunds</div>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${stats.pending_amount.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Transaction List */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Transactions ({filteredTransactions.length})</h3>
          </div>
          <div className="p-6">
            {filteredTransactions.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Transaction</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Provider</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredTransactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{transaction.reference_number}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{transaction.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(transaction.transaction_type)}`}>
                            {getIcon(transaction.transaction_type)}
                            {transaction.transaction_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.booking_info?.provider_name || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          ${transaction.amount.toFixed(2)}
                          {transaction.fee_amount > 0 && (
                            <div className="text-xs text-gray-500">Fee: ${transaction.fee_amount.toFixed(2)}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                            {getStatusIcon(transaction.status)}
                            {transaction.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(transaction.transaction_date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => downloadReceipt(transaction)}
                            className="text-logo-green hover:text-green-600 transition-colors"
                            title="Download Receipt"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <CreditCard className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
                <p className="text-gray-600">Your transactions will appear here after bookings are completed.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingTransactionPage
