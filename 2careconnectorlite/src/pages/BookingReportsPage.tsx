import React, { useState, useEffect } from 'react'
import { FileText, Download, Calendar, TrendingUp, DollarSign, Users, Clock, BarChart3 } from 'lucide-react'
import { supabase } from '../lib/supabase'

const BookingReportsPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })

  const [reportData, setReportData] = useState<any[]>([])
  const [summary, setSummary] = useState({
    total_bookings: 0,
    total_revenue: 0,
    total_hours: 0,
    completion_rate: 0
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadReportData()
      }
      setLoading(false)
    }
    getUser()
  }, [dateRange])

  const loadReportData = async () => {
    if (!user) return

    try {
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          id, service_date, duration_hours, status, total_amount,
          profiles!bookings_provider_id_fkey(full_name, role)
        `)
        .gte('service_date', dateRange.start)
        .lte('service_date', dateRange.end)
        .eq('client_id', user.id)

      if (error) throw error

      const data = bookings || []
      setReportData(data)

      const totalBookings = data.length
      const totalRevenue = data.reduce((sum, b) => sum + (b.total_amount || 0), 0)
      const totalHours = data.reduce((sum, b) => sum + (b.duration_hours || 0), 0)
      const completed = data.filter(b => b.status === 'completed').length

      setSummary({
        total_bookings: totalBookings,
        total_revenue: totalRevenue,
        total_hours: totalHours,
        completion_rate: totalBookings > 0 ? (completed / totalBookings) * 100 : 0
      })
    } catch (error) {
      console.error('Error loading report data:', error)
    }
  }

  const exportToCSV = () => {
    setGenerating(true)
    const csvData = [
      'Date,Provider,Status,Amount,Duration',
      ...reportData.map(b => 
        `${b.service_date},"${b.profiles?.full_name || 'Unknown'}",${b.status},$${b.total_amount || 0},${b.duration_hours || 0}h`
      )
    ].join('\n')

    const blob = new Blob([csvData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `booking-report-${dateRange.start}-to-${dateRange.end}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    
    setTimeout(() => setGenerating(false), 1000)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading reports...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <FileText className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Reports Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to view your booking reports.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Reports</h1>
            <p className="text-gray-600">Comprehensive analytics and reporting for your bookings</p>
          </div>
          <button
            onClick={exportToCSV}
            disabled={generating || reportData.length === 0}
            className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            {generating ? 'Generating...' : 'Export CSV'}
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Calendar className="w-8 h-8 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{summary.total_bookings}</div>
                <div className="text-sm text-gray-600">Total Bookings</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <DollarSign className="w-8 h-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">${summary.total_revenue.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Total Revenue</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <Clock className="w-8 h-8 text-purple-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{summary.total_hours}</div>
                <div className="text-sm text-gray-600">Total Hours</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-4">
              <TrendingUp className="w-8 h-8 text-orange-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{summary.completion_rate.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">Completion Rate</div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Report */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Detailed Booking Report</h3>
          </div>
          
          <div className="p-6">
            {reportData.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Provider</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Duration</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportData.map((booking) => (
                      <tr key={booking.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(booking.service_date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {booking.profiles?.full_name || 'Unknown Provider'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {booking.duration_hours || 0}h
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            booking.status === 'completed' ? 'bg-green-100 text-green-800' :
                            booking.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                            booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {booking.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          ${booking.total_amount || 0}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                <p className="text-gray-600">Try adjusting your date range to see booking data.</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/booking-search'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
              <div className="font-medium text-gray-900">Find Providers</div>
              <div className="text-sm text-gray-600">Search for new providers</div>
            </button>
            <button
              onClick={() => window.location.href = '/my-bookings'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <Calendar className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">My Bookings</div>
              <div className="text-sm text-gray-600">View current bookings</div>
            </button>
            <button
              onClick={() => window.location.href = '/booking-analytics'}
              className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
            >
              <BarChart3 className="w-6 h-6 text-logo-green mb-2" />
              <div className="font-medium text-gray-900">Analytics</div>
              <div className="text-sm text-gray-600">Advanced analytics</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingReportsPage
