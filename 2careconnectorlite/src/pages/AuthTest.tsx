import { useState } from 'react'
import { useN<PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff, ArrowRight, Shield, CheckCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function AuthTest() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('J4913836j')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [emailFocused, setEmailFocused] = useState(false)
  const [passwordFocused, setPasswordFocused] = useState(false)
  const navigate = useNavigate()

  const testAuth = () => {
    console.log('=== STARTING REACT AUTH TEST ===')
    setLoading(true)
    setError('')
    setSuccess('')

    // Use setTimeout to ensure state updates are processed
    setTimeout(async () => {
      try {
        console.log('Testing with email:', email)
        console.log('Supabase client available:', !!supabase)

        const authResult = await supabase.auth.signInWithPassword({
          email: email,
          password: password,
        })

        console.log('Auth result:', authResult)

        if (authResult.error) {
          console.error('Authentication failed:', authResult.error)
          setError(`Auth Error: ${authResult.error.message}`)
        } else {
          console.log('Authentication successful:', authResult.data.user)
          setSuccess(`Success! User: ${authResult.data.user?.email}`)

          // Navigate to dashboard
          setTimeout(() => {
            console.log('Navigating to dashboard...')
            navigate('/dashboard')
          }, 1500)
        }
      } catch (err) {
        console.error('Catch block error:', err)
        setError(`Catch Error: ${err}`)
      } finally {
        setLoading(false)
      }
    }, 100)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-primary p-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 right-20 w-32 h-32 rounded-full bg-accent animate-pulse-subtle"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full bg-accent animate-pulse-subtle" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="max-w-md w-full relative z-10">
        {/* Header Section */}
        <div className="text-center mb-8 animate-fadeInUp">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-accent mb-6">
            <Shield className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-light text-primary mb-2">Welcome Back</h1>
          <p className="text-secondary">Sign in to your care network account</p>
        </div>

        {/* Form Container */}
        <div className="bg-primary rounded-2xl shadow-2xl p-8 border border-light animate-fadeInScale" style={{animationDelay: '0.2s'}}>
          <div className="space-y-6">
            {/* Email Field */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-primary">Email Address</label>
              <div className={`relative transition-all duration-300 ${emailFocused ? 'transform scale-[1.02]' : ''}`}>
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Mail className={`w-5 h-5 transition-colors ${emailFocused ? 'text-primary' : 'text-secondary'}`} />
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onFocus={() => setEmailFocused(true)}
                  onBlur={() => setEmailFocused(false)}
                  className={`w-full pl-12 pr-4 py-4 rounded-xl border-2 transition-all duration-300 bg-primary text-primary placeholder-secondary focus:outline-none ${
                    emailFocused ? 'border-primary shadow-lg' : 'border-light hover:border-medium'
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-primary">Password</label>
              <div className={`relative transition-all duration-300 ${passwordFocused ? 'transform scale-[1.02]' : ''}`}>
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Lock className={`w-5 h-5 transition-colors ${passwordFocused ? 'text-primary' : 'text-secondary'}`} />
                </div>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onFocus={() => setPasswordFocused(true)}
                  onBlur={() => setPasswordFocused(false)}
                  className={`w-full pl-12 pr-12 py-4 rounded-xl border-2 transition-all duration-300 bg-primary text-primary placeholder-secondary focus:outline-none ${
                    passwordFocused ? 'border-primary shadow-lg' : 'border-light hover:border-medium'
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-secondary hover:text-primary transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Sign In Button */}
            <button
              onClick={testAuth}
              disabled={loading}
              className={`group w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 transform focus:outline-none focus:ring-4 focus:ring-accent ${
                loading
                  ? 'bg-secondary text-secondary cursor-not-allowed'
                  : 'button-primary hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]'
              }`}
            >
              <div className="flex items-center justify-center gap-3">
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-secondary border-t-primary rounded-full animate-spin"></div>
                    Signing In...
                  </>
                ) : (
                  <>
                    Sign In
                    <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                  </>
                )}
              </div>
            </button>

            {/* Error Message */}
            {error && (
              <div className="p-4 bg-error-light border-2 border-error rounded-xl animate-fadeInScale">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-error flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                  <p className="text-error font-medium">{error}</p>
                </div>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="p-4 bg-success border-2 border-success rounded-xl animate-fadeInScale">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-success flex-shrink-0" />
                  <p className="text-success font-medium">{success}</p>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-secondary text-sm">
              Don't have an account?{' '}
              <Link to="/sign-up" className="text-primary font-semibold hover:underline transition-colors">
                Sign up here
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-6 text-center">
          <Link
            to="/"
            className="inline-flex items-center gap-2 text-secondary hover:text-primary transition-colors text-sm font-medium"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
