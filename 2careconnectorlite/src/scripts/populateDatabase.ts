import { supabase } from '../lib/supabase'

// <PERSON>ript to populate database with sample caregiver data for testing
async function populateDatabase() {
  console.log('Starting database population...')

  // Sample caregiver data - using only columns that exist in profiles table
  const sampleCaregivers = [
    {
      id: '372e1027-cd9c-426e-9871-bea29252e169',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: 'micha<PERSON>.<EMAIL>',
      role: 'caregiver'
    },
    {
      id: '8f3d2c1b-9e4a-4b5c-8d7e-6f9a8b7c6d5e',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: '<EMAIL>',
      role: 'caregiver'
    },
    {
      id: '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: 'sarah.joh<PERSON>@example.com',
      role: 'caregiver'
    }
  ]

  try {
    // Insert sample caregivers
    const { data, error } = await supabase
      .from('profiles')
      .upsert(sampleCaregivers, { onConflict: 'id' })
      .select()

    if (error) {
      console.error('Error inserting caregivers:', error)
      return
    }

    console.log('Successfully inserted caregivers:', data)

    // Also create some sample companions - using only columns that exist
    const sampleCompanions = [
      {
        id: 'c0a81a2b-3c4d-5e6f-7a8b-9c0d1e2f3a4b',
        first_name: 'Lisa',
        last_name: 'Wong',
        email: '<EMAIL>',
        role: 'companion'
      }
    ]

    const { data: companionData, error: companionError } = await supabase
      .from('profiles')
      .upsert(sampleCompanions, { onConflict: 'id' })
      .select()

    if (companionError) {
      console.error('Error inserting companions:', companionError)
    } else {
      console.log('Successfully inserted companions:', companionData)
    }

    console.log('Database population completed!')

  } catch (error) {
    console.error('Error populating database:', error)
  }
}

// Run the population script
// populateDatabase() // Commented out to prevent automatic execution
