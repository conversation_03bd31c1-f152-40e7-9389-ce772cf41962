import { supabase } from '../lib/supabase'

export interface User {
  id: string
  email: string
  first_name?: string
  last_name?: string
  user_type: 'client' | 'provider' | 'caregiver'
  profile_image_url?: string
  role?: string
  full_name?: string
}

class AuthService {
  private currentUser: User | null = null

  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        this.currentUser = null
        return null
      }

      // Get user profile from database
      const { data: userProfile, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        this.currentUser = null
        return null
      }

      this.currentUser = {
        id: userProfile.id,
        email: userProfile.email,
        first_name: userProfile.first_name,
        last_name: userProfile.last_name,
        user_type: userProfile.role || 'client',
        profile_image_url: userProfile.avatar_url,
        role: userProfile.role,
        full_name: userProfile.full_name
      }

      return this.currentUser
    } catch (error) {
      console.error('Error getting current user:', error)
      this.currentUser = null
      return null
    }
  }

  async signIn(email: string, password: string): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        return { user: null, error: error.message }
      }

      if (data.user) {
        const user = await this.getCurrentUser()
        return { user, error: null }
      }

      return { user: null, error: 'Login failed' }
    } catch (error) {
      return { user: null, error: 'An unexpected error occurred' }
    }
  }

  async signUp(userData: {
    email: string
    password: string
    first_name: string
    last_name: string
    user_type: 'client' | 'provider' | 'caregiver'
  }): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            user_type: userData.user_type
          }
        }
      })

      if (error) {
        return { user: null, error: error.message }
      }

      if (data.user) {
        // Create user profile in database
        const { error: profileError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .insert([
            {
              id: data.user.id,
              email: userData.email,
              first_name: userData.first_name,
              last_name: userData.last_name,
              role: userData.user_type,
              full_name: `${userData.first_name} ${userData.last_name}`.trim(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ])

        if (profileError) {
          console.error('Error creating user profile:', profileError)
        }

        const user = await this.getCurrentUser()
        return { user, error: null }
      }

      return { user: null, error: 'Registration failed' }
    } catch (error) {
      return { user: null, error: 'An unexpected error occurred' }
    }
  }

  async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut()
      this.currentUser = null
      
      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    }
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null
  }

  getUser(): User | null {
    return this.currentUser
  }
}

export const authService = new AuthService()
